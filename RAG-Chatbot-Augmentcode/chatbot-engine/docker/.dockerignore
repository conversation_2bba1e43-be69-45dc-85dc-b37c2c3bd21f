# Docker ignore file for production builds

# Version control
.git
.gitignore
.gitattributes

# Documentation
*.md
docs/
README*
CHANGELOG*
LICENSE*

# Development files
.env.example
.env.local
.env.development
docker-compose.dev.yml
docker-compose.override.yml

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Logs
*.log
logs/
chatbot-engine/logs/

# Temporary files
tmp/
temp/
.tmp/

# Data files (exclude from image)
data/
*.db
*.sqlite
*.sqlite3

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backup files
*.bak
*.backup
*.old

# Cache directories
.cache/
.mypy_cache/
.pytest_cache/

# Development tools
Makefile.dev
scripts/dev/
tools/dev/

# CI/CD files (exclude from production image)
.github/
.gitlab-ci.yml
.travis.yml
Jenkinsfile

# Security files
*.pem
*.key
*.crt
secrets/
.secrets/

# Large files that shouldn't be in container
*.tar.gz
*.zip
*.rar
*.7z

# Monitoring and profiling
.profile
*.prof
