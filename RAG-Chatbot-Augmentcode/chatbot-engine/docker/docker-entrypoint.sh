#!/bin/bash
# Production-ready entrypoint script for RAG Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Environment validation
validate_environment() {
    log "Validating environment variables..."
    
    # Required environment variables
    required_vars=(
        "GOOGLE_API_KEY"
        "COHERE_API_KEY"
        "MILVUS_HOST"
        "REDIS_HOST"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            error "Required environment variable $var is not set"
        fi
    done
    
    # Validate API keys format (basic check)
    if [[ ! "$GOOGLE_API_KEY" =~ ^[A-Za-z0-9_-]+$ ]]; then
        warn "GOOGLE_API_KEY format may be invalid"
    fi
    
    if [[ ! "$COHERE_API_KEY" =~ ^[A-Za-z0-9_-]+$ ]]; then
        warn "COHERE_API_KEY format may be invalid"
    fi
    
    log "Environment validation completed"
}

# Wait for dependencies
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-60}
    
    log "Waiting for $service_name at $host:$port..."
    
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" 2>/dev/null; then
            log "$service_name is ready"
            return 0
        fi
        sleep 1
    done
    
    error "$service_name at $host:$port is not available after ${timeout}s"
}

# Health check function
health_check() {
    local service_type=$1
    
    case $service_type in
        "api")
            curl -f http://localhost:8000/health || return 1
            ;;
        "worker")
            celery -A chatbot-engine.src.shared.celery_app inspect ping -d celery@$HOSTNAME || return 1
            ;;
        *)
            error "Unknown service type: $service_type"
            ;;
    esac
}

# Initialize application
initialize_app() {
    log "Initializing application..."
    
    # Create necessary directories
    mkdir -p /app/chatbot-engine/logs
    mkdir -p /app/chatbot-engine/data
    
    # Set proper permissions
    chmod 755 /app/chatbot-engine/logs
    chmod 755 /app/chatbot-engine/data
    
    log "Application initialization completed"
}

# Main execution
main() {
    log "Starting RAG Chatbot container..."
    
    # Validate environment
    validate_environment
    
    # Initialize application
    initialize_app
    
    # Wait for dependencies
    if [ "$ENVIRONMENT" = "production" ]; then
        wait_for_service "$MILVUS_HOST" "${MILVUS_PORT:-19530}" "Milvus"
        wait_for_service "$REDIS_HOST" "${REDIS_PORT:-6379}" "Redis"
    fi
    
    # Execute the main command
    log "Starting service: $*"
    exec "$@"
}

# Run main function with all arguments
main "$@"
