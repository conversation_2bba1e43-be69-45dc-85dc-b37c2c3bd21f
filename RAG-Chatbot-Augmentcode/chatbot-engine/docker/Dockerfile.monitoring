# Dockerfile for monitoring stack (Prometheus + Grafana)
FROM prom/prometheus:latest as prometheus

# Copy Prometheus configuration
COPY chatbot-engine/docker/monitoring/prometheus.yml /etc/prometheus/prometheus.yml
COPY chatbot-engine/docker/monitoring/alert.rules.yml /etc/prometheus/alert.rules.yml

# Expose Prometheus port
EXPOSE 9090

# Multi-stage build for Grafana with custom dashboards
FROM grafana/grafana:latest as grafana

# Set environment variables
ENV GF_SECURITY_ADMIN_PASSWORD=admin \
    GF_USERS_ALLOW_SIGN_UP=false \
    GF_INSTALL_PLUGINS=grafana-piechart-panel

# Copy Grafana configuration
COPY chatbot-engine/docker/monitoring/grafana/dashboards /var/lib/grafana/dashboards
COPY chatbot-engine/docker/monitoring/grafana/provisioning /etc/grafana/provisioning

# Create grafana user
USER grafana

# Expose Grafana port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1
