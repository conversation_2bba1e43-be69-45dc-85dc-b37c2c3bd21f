# Production-optimized Nginx configuration
worker_processes auto;
worker_rlimit_nofile 65535;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Upstream configuration with load balancing
    upstream api_backend {
        least_conn;
        server api:8000 max_fails=3 fail_timeout=30s;
        # Additional API instances for horizontal scaling
        # server api-2:8000 max_fails=3 fail_timeout=30s;
        # server api-3:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=20r/s;
    limit_req_zone $binary_remote_addr zone=admin_limit:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=chat_limit:10m rate=10r/s;

    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn_zone $server_name zone=conn_limit_per_server:10m;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    client_max_body_size 10M;
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # Buffer sizes
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        application/rss+xml
        application/x-javascript
        image/svg+xml;

    # Enhanced security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # Enhanced logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time" '
                    'cache="$upstream_cache_status" host="$host"';

    log_format json_combined escape=json
        '{'
        '"time_local":"$time_local",'
        '"remote_addr":"$remote_addr",'
        '"remote_user":"$remote_user",'
        '"request":"$request",'
        '"status": "$status",'
        '"body_bytes_sent":"$body_bytes_sent",'
        '"request_time":"$request_time",'
        '"http_referrer":"$http_referer",'
        '"http_user_agent":"$http_user_agent",'
        '"upstream_response_time":"$upstream_response_time",'
        '"upstream_connect_time":"$upstream_connect_time",'
        '"upstream_header_time":"$upstream_header_time"'
        '}';

    access_log /var/log/nginx/access.log main;
    access_log /var/log/nginx/access.json json_combined;
    error_log /var/log/nginx/error.log warn;

    # HTTP server block (redirects to HTTPS in production)
    server {
        listen 80;
        server_name _;

        # Health check endpoint (allow HTTP for load balancer health checks)
        location /health {
            limit_conn conn_limit_per_ip 10;
            proxy_pass http://api_backend/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Cache health checks
            proxy_cache_valid 200 1m;
            proxy_cache_key "$scheme$request_method$host$request_uri";
        }

        # Redirect all other HTTP traffic to HTTPS (uncomment for production)
        # location / {
        #     return 301 https://$server_name$request_uri;
        # }

        # For development/testing, allow HTTP access
        location / {
            return 404;
        }
    }

    # HTTPS server block for production
    server {
        listen 443 ssl http2;
        server_name localhost;  # Change to your domain in production

        # SSL configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_stapling on;
        ssl_stapling_verify on;

        # Connection limits
        limit_conn conn_limit_per_ip 20;
        limit_conn conn_limit_per_server 1000;

        # Health check endpoint
        location /health {
            limit_req zone=api_limit burst=10 nodelay;

            proxy_pass http://api_backend/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Health check caching
            proxy_cache_valid 200 30s;
        }

        # API endpoints with enhanced rate limiting
        location /api/ {
            limit_req zone=api_limit burst=30 nodelay;
            limit_conn conn_limit_per_ip 10;

            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";
            proxy_http_version 1.1;

            # Enhanced timeouts
            proxy_connect_timeout 10s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            # Optimized buffering
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 16 8k;
            proxy_busy_buffers_size 16k;
        }

        # Chat streaming endpoint with optimized settings
        location /chat/stream {
            limit_req zone=chat_limit burst=15 nodelay;
            limit_conn conn_limit_per_ip 5;

            proxy_pass http://api_backend/chat/stream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Streaming optimizations
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 600s;
            proxy_send_timeout 600s;
            proxy_connect_timeout 10s;

            # SSE/streaming headers
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            chunked_transfer_encoding off;

            # Disable proxy timeouts for streaming
            proxy_request_buffering off;
        }

        # Admin endpoints with enhanced security
        location /admin/ {
            limit_req zone=admin_limit burst=5 nodelay;
            limit_conn conn_limit_per_ip 3;

            # Additional security for admin endpoints
            # Add IP whitelist for production
            # allow 10.0.0.0/8;
            # allow **********/12;
            # allow ***********/16;
            # deny all;

            proxy_pass http://api_backend/admin/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";
            proxy_http_version 1.1;

            # Admin endpoint timeouts
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Metrics endpoint (internal monitoring only)
        location /metrics {
            # Restrict to internal networks only
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;

            proxy_pass http://api_backend/metrics;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # No caching for metrics
            proxy_cache off;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }

        # Static files (if any) with caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options nosniff;
        }

        # Security: Block common attack patterns
        location ~* \.(php|asp|aspx|jsp)$ {
            return 444;
        }

        location ~* /\. {
            deny all;
        }

        # Default location - return 404 for unmatched routes
        location / {
            return 404;
        }
    }
}
