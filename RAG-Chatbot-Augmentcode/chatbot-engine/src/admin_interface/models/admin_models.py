"""
Admin interface data models.

This module defines Pydantic models for admin interface API responses
and request/response structures.
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field


class ServiceHealthResponse(BaseModel):
    """Service health status response."""
    name: str
    status: str
    response_time_ms: Optional[float] = None
    last_check: Optional[datetime] = None
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class SystemMetricsResponse(BaseModel):
    """System performance metrics response."""
    timestamp: datetime
    cpu_usage_percent: float
    memory_usage_percent: float
    disk_usage_percent: float
    active_connections: int
    total_requests: int
    avg_response_time_ms: float
    error_rate_percent: float


class SystemStatusResponse(BaseModel):
    """Overall system status response."""
    overall_health: str
    services: List[ServiceHealthResponse]
    metrics: SystemMetricsResponse
    uptime_seconds: int
    version: str


class PipelineTaskResponse(BaseModel):
    """Pipeline task status response."""
    task_id: str
    pipeline_task_id: Optional[str] = None
    mode: Optional[str] = None
    source_ids: Optional[List[str]] = None
    status: str
    progress: int = 0
    message: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


class AnalyticsResponse(BaseModel):
    """Analytics data response."""
    period: Dict[str, Any]
    total_queries: int = 0
    daily_breakdown: List[Dict[str, Any]] = []
    query_types: Dict[str, int] = {}
    languages: Dict[str, int] = {}
    hourly_distribution: List[Dict[str, Any]] = []


class ConfigValidationResponse(BaseModel):
    """Configuration validation response."""
    valid: bool
    errors: List[str] = []
    warnings: List[str] = []
    config_name: str


class BulkOperationResponse(BaseModel):
    """Bulk operation response."""
    success: List[str] = []
    failed: List[Dict[str, str]] = []
    total_processed: int = 0
    success_count: int = 0
    failure_count: int = 0


class SourceSummaryResponse(BaseModel):
    """Data source summary response."""
    total_sources: int
    enabled_sources: int
    disabled_sources: int
    sources_by_type: Dict[str, int]
    last_updated: str


class PipelineStatisticsResponse(BaseModel):
    """Pipeline statistics response."""
    total_tasks: int
    active_tasks: int
    completed_tasks: int
    status_counts: Dict[str, int]
    success_rate_percent: float
    recent_tasks_24h: int
    last_updated: str


class WorkerStatusResponse(BaseModel):
    """Celery worker status response."""
    total_workers: int
    active_workers: int
    workers: Dict[str, Dict[str, Any]]
    total_active_tasks: int
    total_reserved_tasks: int


class UsageSummaryResponse(BaseModel):
    """Usage summary response."""
    queries: Dict[str, int]
    sessions: Dict[str, Any]
    performance: Dict[str, float]
    top_query_types: Dict[str, int]
    top_languages: Dict[str, int]
    last_updated: str


class ConfigHistoryEntry(BaseModel):
    """Configuration history entry."""
    config_name: str
    timestamp: str
    action: str
    previous_config: Optional[Dict[str, Any]] = None
    config_data: Optional[Dict[str, Any]] = None


class ConfigBackupResponse(BaseModel):
    """Configuration backup response."""
    success: bool
    config_name: str
    backup_timestamp: str
    message: str


class ConfigRestoreResponse(BaseModel):
    """Configuration restore response."""
    success: bool
    config_name: str
    restored_from: str
    message: str


class ConfigUpdateResponse(BaseModel):
    """Configuration update response."""
    success: bool
    config_name: str
    updated_at: str
    message: str


class AdminDashboardData(BaseModel):
    """Complete admin dashboard data."""
    system_status: SystemStatusResponse
    usage_summary: UsageSummaryResponse
    source_summary: SourceSummaryResponse
    pipeline_statistics: PipelineStatisticsResponse
    worker_status: WorkerStatusResponse
    recent_tasks: List[PipelineTaskResponse]
    last_updated: datetime = Field(default_factory=datetime.now)
