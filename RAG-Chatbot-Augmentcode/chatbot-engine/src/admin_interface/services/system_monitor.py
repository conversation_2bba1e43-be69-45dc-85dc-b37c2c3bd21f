"""
System monitoring service for admin interface.

This module provides comprehensive system monitoring capabilities including:
- Service health checks (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Celery)
- Performance metrics collection
- Resource usage monitoring
- Real-time system status
"""

import asyncio
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict

import redis
from pymilvus import connections, utility
from celery import Celery

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.celery_app import celery_app

logger = get_logger(__name__)


@dataclass
class ServiceHealth:
    """Service health status."""
    name: str
    status: str  # "healthy", "degraded", "unhealthy", "unknown"
    response_time_ms: Optional[float] = None
    last_check: Optional[datetime] = None
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime
    cpu_usage_percent: float
    memory_usage_percent: float
    disk_usage_percent: float
    active_connections: int
    total_requests: int
    avg_response_time_ms: float
    error_rate_percent: float


@dataclass
class SystemStatus:
    """Overall system status."""
    overall_health: str
    services: List[ServiceHealth]
    metrics: SystemMetrics
    uptime_seconds: int
    version: str


class SystemMonitor:
    """
    Comprehensive system monitoring service.
    
    Provides real-time monitoring of all system components including
    external services, performance metrics, and health status.
    """
    
    def __init__(self):
        """Initialize the system monitor."""
        self.settings = get_settings()
        self.start_time = time.time()
        self._redis_client = None
        self._milvus_connected = False
        
        # Metrics storage (in production, use proper time-series DB)
        self._metrics_history: List[SystemMetrics] = []
        self._max_history_size = 1000
        
        logger.info("System monitor initialized")
    
    async def initialize(self):
        """Initialize connections and start monitoring."""
        try:
            await self._initialize_redis()
            await self._initialize_milvus()
            logger.info("System monitor initialization completed")
        except Exception as e:
            logger.error(f"System monitor initialization failed: {e}", exc_info=True)
    
    async def _initialize_redis(self):
        """Initialize Redis connection."""
        try:
            self._redis_client = redis.Redis(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                db=self.settings.redis_db,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            # Test connection
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.ping
            )
            logger.info("Redis connection initialized")
        except Exception as e:
            logger.error(f"Redis initialization failed: {e}")
            self._redis_client = None
    
    async def _initialize_milvus(self):
        """Initialize Milvus connection."""
        try:
            connections.connect(
                alias="default",
                host=self.settings.milvus_host,
                port=self.settings.milvus_port,
                timeout=5
            )
            self._milvus_connected = True
            logger.info("Milvus connection initialized")
        except Exception as e:
            logger.error(f"Milvus initialization failed: {e}")
            self._milvus_connected = False
    
    async def check_service_health(self, service_name: str) -> ServiceHealth:
        """Check health of a specific service."""
        start_time = time.time()
        
        try:
            if service_name == "redis":
                return await self._check_redis_health(start_time)
            elif service_name == "milvus":
                return await self._check_milvus_health(start_time)
            elif service_name == "celery":
                return await self._check_celery_health(start_time)
            elif service_name == "api":
                return await self._check_api_health(start_time)
            else:
                return ServiceHealth(
                    name=service_name,
                    status="unknown",
                    error_message=f"Unknown service: {service_name}"
                )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ServiceHealth(
                name=service_name,
                status="unhealthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_redis_health(self, start_time: float) -> ServiceHealth:
        """Check Redis health."""
        try:
            if not self._redis_client:
                await self._initialize_redis()
            
            if not self._redis_client:
                raise Exception("Redis client not available")
            
            # Test basic operations
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.ping
            )
            
            info = await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.info
            )
            
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                name="redis",
                status="healthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                details={
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_human": info.get("used_memory_human", "unknown"),
                    "uptime_in_seconds": info.get("uptime_in_seconds", 0)
                }
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ServiceHealth(
                name="redis",
                status="unhealthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_milvus_health(self, start_time: float) -> ServiceHealth:
        """Check Milvus health."""
        try:
            if not self._milvus_connected:
                await self._initialize_milvus()
            
            if not self._milvus_connected:
                raise Exception("Milvus connection not available")
            
            # Test connection and get server info
            server_version = utility.get_server_version()
            
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                name="milvus",
                status="healthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                details={
                    "server_version": server_version,
                    "connection_status": "connected"
                }
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ServiceHealth(
                name="milvus",
                status="unhealthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_celery_health(self, start_time: float) -> ServiceHealth:
        """Check Celery health."""
        try:
            # Get active workers and tasks
            inspect = celery_app.control.inspect()
            
            # Run in executor to avoid blocking
            active_workers = await asyncio.get_event_loop().run_in_executor(
                None, inspect.active
            )
            
            if not active_workers:
                return ServiceHealth(
                    name="celery",
                    status="degraded",
                    response_time_ms=(time.time() - start_time) * 1000,
                    last_check=datetime.now(),
                    error_message="No active workers found"
                )
            
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                name="celery",
                status="healthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                details={
                    "active_workers": len(active_workers),
                    "worker_names": list(active_workers.keys())
                }
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ServiceHealth(
                name="celery",
                status="unhealthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_api_health(self, start_time: float) -> ServiceHealth:
        """Check API health."""
        try:
            # Simple health check - API is healthy if this method is called
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                name="api",
                status="healthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                details={
                    "uptime_seconds": int(time.time() - self.start_time)
                }
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ServiceHealth(
                name="api",
                status="unhealthy",
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )

    async def get_system_metrics(self) -> SystemMetrics:
        """Get current system performance metrics."""
        try:
            # CPU and memory usage
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # Network connections (approximate)
            connections = len(psutil.net_connections())

            # Create metrics object
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_usage_percent=cpu_percent,
                memory_usage_percent=memory.percent,
                disk_usage_percent=disk.percent,
                active_connections=connections,
                total_requests=0,  # TODO: Implement request counting
                avg_response_time_ms=0.0,  # TODO: Implement response time tracking
                error_rate_percent=0.0  # TODO: Implement error rate tracking
            )

            # Store in history
            self._metrics_history.append(metrics)
            if len(self._metrics_history) > self._max_history_size:
                self._metrics_history.pop(0)

            return metrics
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}", exc_info=True)
            # Return default metrics on error
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_usage_percent=0.0,
                memory_usage_percent=0.0,
                disk_usage_percent=0.0,
                active_connections=0,
                total_requests=0,
                avg_response_time_ms=0.0,
                error_rate_percent=100.0
            )

    async def get_system_status(self) -> SystemStatus:
        """Get comprehensive system status."""
        try:
            # Check all services
            services = []
            service_names = ["redis", "milvus", "celery", "api"]

            # Check services in parallel
            health_checks = [
                self.check_service_health(service) for service in service_names
            ]
            services = await asyncio.gather(*health_checks)

            # Get current metrics
            metrics = await self.get_system_metrics()

            # Determine overall health
            unhealthy_services = [s for s in services if s.status == "unhealthy"]
            degraded_services = [s for s in services if s.status == "degraded"]

            if unhealthy_services:
                overall_health = "unhealthy"
            elif degraded_services:
                overall_health = "degraded"
            else:
                overall_health = "healthy"

            return SystemStatus(
                overall_health=overall_health,
                services=services,
                metrics=metrics,
                uptime_seconds=int(time.time() - self.start_time),
                version=self.settings.app_version
            )
        except Exception as e:
            logger.error(f"Error getting system status: {e}", exc_info=True)
            # Return minimal status on error
            return SystemStatus(
                overall_health="unknown",
                services=[],
                metrics=await self.get_system_metrics(),
                uptime_seconds=int(time.time() - self.start_time),
                version=self.settings.app_version
            )

    async def get_metrics_history(self, hours: int = 24) -> List[SystemMetrics]:
        """Get metrics history for the specified number of hours."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            return [
                m for m in self._metrics_history
                if m.timestamp >= cutoff_time
            ]
        except Exception as e:
            logger.error(f"Error getting metrics history: {e}", exc_info=True)
            return []

    def get_service_health_dict(self, service_health: ServiceHealth) -> Dict[str, Any]:
        """Convert ServiceHealth to dictionary."""
        return asdict(service_health)

    def get_system_metrics_dict(self, metrics: SystemMetrics) -> Dict[str, Any]:
        """Convert SystemMetrics to dictionary."""
        result = asdict(metrics)
        # Convert datetime to ISO string
        result['timestamp'] = metrics.timestamp.isoformat()
        return result

    def get_system_status_dict(self, status: SystemStatus) -> Dict[str, Any]:
        """Convert SystemStatus to dictionary."""
        return {
            "overall_health": status.overall_health,
            "services": [self.get_service_health_dict(s) for s in status.services],
            "metrics": self.get_system_metrics_dict(status.metrics),
            "uptime_seconds": status.uptime_seconds,
            "version": status.version
        }

    async def cleanup(self):
        """Cleanup resources."""
        try:
            if self._redis_client:
                await asyncio.get_event_loop().run_in_executor(
                    None, self._redis_client.close
                )

            if self._milvus_connected:
                connections.disconnect("default")

            logger.info("System monitor cleanup completed")
        except Exception as e:
            logger.error(f"Error during system monitor cleanup: {e}", exc_info=True)
