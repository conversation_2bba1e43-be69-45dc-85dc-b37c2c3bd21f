"""
Analytics service for admin interface.

This module provides comprehensive analytics and reporting capabilities including:
- User session analytics
- Query pattern analysis
- Performance metrics and trends
- Usage statistics and reporting
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from collections import defaultdict, Counter
from pathlib import Path

import redis

import sys

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.config import get_settings
from shared.logging_config import get_logger
from online_pipeline.session_manager import SessionManager

logger = get_logger(__name__)


class AnalyticsService:
    """
    Comprehensive analytics service for admin interface.
    
    Provides detailed analytics on system usage, performance,
    and user behavior patterns.
    """
    
    def __init__(self):
        """Initialize the analytics service."""
        self.settings = get_settings()
        self.session_manager = SessionManager()
        self._redis_client = None
        self._initialized = False
        
        # Analytics keys
        self.analytics_prefix = "analytics:"
        self.query_stats_key = f"{self.analytics_prefix}query_stats"
        self.session_stats_key = f"{self.analytics_prefix}session_stats"
        self.performance_stats_key = f"{self.analytics_prefix}performance_stats"
        
        logger.info("Analytics service initialized")
    
    async def initialize(self):
        """Initialize the analytics service."""
        try:
            await self.session_manager.initialize()
            
            # Initialize Redis connection
            self._redis_client = redis.Redis(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                db=self.settings.redis_db,
                decode_responses=True
            )
            
            # Test connection
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.ping
            )
            
            self._initialized = True
            logger.info("Analytics service initialization completed")
        except Exception as e:
            logger.error(f"Analytics service initialization failed: {e}", exc_info=True)
            raise
    
    async def record_query(self, query_data: Dict[str, Any]):
        """Record a query for analytics."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Extract analytics data
            timestamp = datetime.now()
            query_length = len(query_data.get("query", ""))
            query_type = query_data.get("query_type", "unknown")
            language = query_data.get("language", "unknown")
            response_time_ms = query_data.get("response_time_ms", 0)
            
            # Store in Redis with daily granularity
            date_key = timestamp.strftime("%Y-%m-%d")
            hour_key = timestamp.strftime("%H")
            
            # Update daily stats
            daily_key = f"{self.query_stats_key}:daily:{date_key}"
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.hincrby, daily_key, "total_queries", 1
            )
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.hincrby, daily_key, f"type_{query_type}", 1
            )
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.hincrby, daily_key, f"lang_{language}", 1
            )
            
            # Update hourly stats
            hourly_key = f"{self.query_stats_key}:hourly:{date_key}:{hour_key}"
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.hincrby, hourly_key, "total_queries", 1
            )
            
            # Store response time for performance analysis
            perf_key = f"{self.performance_stats_key}:response_times:{date_key}"
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.lpush, perf_key, response_time_ms
            )
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.ltrim, perf_key, 0, 999  # Keep last 1000 entries
            )
            
            # Set expiration (30 days)
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.expire, daily_key, 30 * 24 * 3600
            )
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.expire, hourly_key, 7 * 24 * 3600
            )
            await asyncio.get_event_loop().run_in_executor(
                None, self._redis_client.expire, perf_key, 30 * 24 * 3600
            )
            
        except Exception as e:
            logger.error(f"Error recording query analytics: {e}", exc_info=True)
    
    async def get_query_analytics(self, days: int = 7) -> Dict[str, Any]:
        """Get query analytics for the specified number of days."""
        try:
            if not self._initialized:
                await self.initialize()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            analytics = {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "total_queries": 0,
                "daily_breakdown": [],
                "query_types": {},
                "languages": {},
                "hourly_distribution": defaultdict(int)
            }
            
            # Collect daily data
            current_date = start_date
            while current_date <= end_date:
                date_key = current_date.strftime("%Y-%m-%d")
                daily_key = f"{self.query_stats_key}:daily:{date_key}"
                
                daily_data = await asyncio.get_event_loop().run_in_executor(
                    None, self._redis_client.hgetall, daily_key
                )
                
                daily_queries = int(daily_data.get("total_queries", 0))
                analytics["total_queries"] += daily_queries
                
                analytics["daily_breakdown"].append({
                    "date": date_key,
                    "queries": daily_queries
                })
                
                # Aggregate query types and languages
                for key, value in daily_data.items():
                    if key.startswith("type_"):
                        query_type = key[5:]  # Remove "type_" prefix
                        analytics["query_types"][query_type] = analytics["query_types"].get(query_type, 0) + int(value)
                    elif key.startswith("lang_"):
                        language = key[5:]  # Remove "lang_" prefix
                        analytics["languages"][language] = analytics["languages"].get(language, 0) + int(value)
                
                # Collect hourly data
                for hour in range(24):
                    hour_key = f"{hour:02d}"
                    hourly_key = f"{self.query_stats_key}:hourly:{date_key}:{hour_key}"
                    hourly_data = await asyncio.get_event_loop().run_in_executor(
                        None, self._redis_client.hgetall, hourly_key
                    )
                    hourly_queries = int(hourly_data.get("total_queries", 0))
                    analytics["hourly_distribution"][hour_key] += hourly_queries
                
                current_date += timedelta(days=1)
            
            # Convert hourly distribution to list
            analytics["hourly_distribution"] = [
                {"hour": f"{hour:02d}", "queries": analytics["hourly_distribution"][f"{hour:02d}"]}
                for hour in range(24)
            ]
            
            logger.info(f"Generated query analytics for {days} days")
            return analytics
        except Exception as e:
            logger.error(f"Error getting query analytics: {e}", exc_info=True)
            return {}
    
    async def get_session_analytics(self, days: int = 7) -> Dict[str, Any]:
        """Get session analytics for the specified number of days."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Get session statistics from session manager
            session_stats = await self.session_manager.get_session_stats()
            
            # Get historical session data (simplified for now)
            analytics = {
                "period": {
                    "days": days,
                    "end_date": datetime.now().isoformat()
                },
                "current_sessions": {
                    "total_active": session_stats.get("total_sessions", 0),
                    "avg_turns_per_session": session_stats.get("avg_turns_per_session", 0),
                    "total_turns": session_stats.get("total_turns", 0)
                },
                "session_duration": {
                    "avg_duration_minutes": 0,  # TODO: Implement session duration tracking
                    "median_duration_minutes": 0
                },
                "user_engagement": {
                    "sessions_with_multiple_turns": 0,  # TODO: Implement engagement metrics
                    "avg_queries_per_session": session_stats.get("avg_turns_per_session", 0)
                }
            }
            
            logger.info(f"Generated session analytics for {days} days")
            return analytics
        except Exception as e:
            logger.error(f"Error getting session analytics: {e}", exc_info=True)
            return {}
    
    async def get_performance_analytics(self, days: int = 7) -> Dict[str, Any]:
        """Get performance analytics for the specified number of days."""
        try:
            if not self._initialized:
                await self.initialize()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            analytics = {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "response_times": {
                    "avg_response_time_ms": 0,
                    "median_response_time_ms": 0,
                    "p95_response_time_ms": 0,
                    "p99_response_time_ms": 0
                },
                "daily_performance": []
            }
            
            all_response_times = []
            
            # Collect performance data for each day
            current_date = start_date
            while current_date <= end_date:
                date_key = current_date.strftime("%Y-%m-%d")
                perf_key = f"{self.performance_stats_key}:response_times:{date_key}"
                
                # Get response times for this day
                response_times = await asyncio.get_event_loop().run_in_executor(
                    None, self._redis_client.lrange, perf_key, 0, -1
                )
                
                daily_times = [float(rt) for rt in response_times if rt]
                all_response_times.extend(daily_times)
                
                if daily_times:
                    daily_avg = sum(daily_times) / len(daily_times)
                    daily_times.sort()
                    daily_median = daily_times[len(daily_times) // 2] if daily_times else 0
                else:
                    daily_avg = 0
                    daily_median = 0
                
                analytics["daily_performance"].append({
                    "date": date_key,
                    "avg_response_time_ms": round(daily_avg, 2),
                    "median_response_time_ms": round(daily_median, 2),
                    "total_queries": len(daily_times)
                })
                
                current_date += timedelta(days=1)
            
            # Calculate overall statistics
            if all_response_times:
                all_response_times.sort()
                n = len(all_response_times)
                
                analytics["response_times"]["avg_response_time_ms"] = round(
                    sum(all_response_times) / n, 2
                )
                analytics["response_times"]["median_response_time_ms"] = round(
                    all_response_times[n // 2], 2
                )
                analytics["response_times"]["p95_response_time_ms"] = round(
                    all_response_times[int(n * 0.95)], 2
                )
                analytics["response_times"]["p99_response_time_ms"] = round(
                    all_response_times[int(n * 0.99)], 2
                )
            
            logger.info(f"Generated performance analytics for {days} days")
            return analytics
        except Exception as e:
            logger.error(f"Error getting performance analytics: {e}", exc_info=True)
            return {}
    
    async def get_usage_summary(self) -> Dict[str, Any]:
        """Get overall usage summary."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Get data for different time periods
            today_analytics = await self.get_query_analytics(days=1)
            week_analytics = await self.get_query_analytics(days=7)
            month_analytics = await self.get_query_analytics(days=30)
            
            session_analytics = await self.get_session_analytics(days=7)
            performance_analytics = await self.get_performance_analytics(days=7)
            
            summary = {
                "queries": {
                    "today": today_analytics.get("total_queries", 0),
                    "this_week": week_analytics.get("total_queries", 0),
                    "this_month": month_analytics.get("total_queries", 0)
                },
                "sessions": {
                    "active_sessions": session_analytics.get("current_sessions", {}).get("total_active", 0),
                    "avg_turns_per_session": session_analytics.get("current_sessions", {}).get("avg_turns_per_session", 0)
                },
                "performance": {
                    "avg_response_time_ms": performance_analytics.get("response_times", {}).get("avg_response_time_ms", 0),
                    "p95_response_time_ms": performance_analytics.get("response_times", {}).get("p95_response_time_ms", 0)
                },
                "top_query_types": week_analytics.get("query_types", {}),
                "top_languages": week_analytics.get("languages", {}),
                "last_updated": datetime.now().isoformat()
            }
            
            logger.info("Generated usage summary")
            return summary
        except Exception as e:
            logger.error(f"Error getting usage summary: {e}", exc_info=True)
            return {}
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            if self._redis_client:
                await asyncio.get_event_loop().run_in_executor(
                    None, self._redis_client.close
                )
            
            if self.session_manager:
                await self.session_manager.cleanup()
                
            logger.info("Analytics service cleanup completed")
        except Exception as e:
            logger.error(f"Error during analytics service cleanup: {e}", exc_info=True)
