"""
Citation management for legal responses.

This module handles the extraction, formatting, and validation of citations
in LLM responses to ensure proper attribution of legal sources.
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from ...shared.logging_config import get_logger
from ..retrieval.result_fusion import FusedResult

logger = get_logger(__name__)


class Citation:
    """Represents a legal citation."""
    
    def __init__(self, 
                 source_id: str,
                 title: str,
                 url: Optional[str] = None,
                 excerpt: Optional[str] = None,
                 page: Optional[str] = None,
                 section: Optional[str] = None,
                 citation_text: Optional[str] = None):
        self.source_id = source_id
        self.title = title
        self.url = url
        self.excerpt = excerpt
        self.page = page
        self.section = section
        self.citation_text = citation_text
        self.created_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert citation to dictionary."""
        return {
            "source_id": self.source_id,
            "title": self.title,
            "url": self.url,
            "excerpt": self.excerpt,
            "page": self.page,
            "section": self.section,
            "citation_text": self.citation_text
        }


class CitationManager:
    """
    Manages citations in legal responses.
    
    This class handles the extraction of citations from LLM responses,
    validates them against source documents, and formats them properly
    for legal documentation standards.
    """
    
    def __init__(self):
        """Initialize the citation manager."""
        self.logger = get_logger(__name__)
        self._citation_patterns = self._load_citation_patterns()
        
    def _load_citation_patterns(self) -> Dict[str, str]:
        """Load regex patterns for different citation formats."""
        return {
            # Standard format: [Source: Document Title]
            "standard": r'\[Source:\s*([^\]]+)\]',
            
            # Legal format: [§ 123 BGB]
            "legal_section": r'\[§\s*(\d+[a-z]?)\s+([A-Z]+)\]',
            
            # Case format: [BGH, Urt. v. 01.01.2020 - I ZR 1/20]
            "case": r'\[([A-Z]+),\s*([^,]+),\s*([^\]]+)\]',
            
            # URL format: [https://example.com]
            "url": r'\[(https?://[^\]]+)\]',
            
            # Page format: [Source: Document Title, p. 123]
            "page": r'\[Source:\s*([^,]+),\s*p\.\s*(\d+)\]',
            
            # Section format: [Source: Document Title, § 123]
            "section": r'\[Source:\s*([^,]+),\s*§\s*(\d+[a-z]?)\]'
        }
    
    def extract_citations(self, response_text: str) -> List[Citation]:
        """
        Extract citations from LLM response text.
        
        Args:
            response_text: The generated response text
            
        Returns:
            List of extracted citations
        """
        try:
            citations = []
            
            # Extract different types of citations
            for citation_type, pattern in self._citation_patterns.items():
                matches = re.finditer(pattern, response_text, re.IGNORECASE)
                
                for match in matches:
                    citation = self._parse_citation_match(citation_type, match)
                    if citation:
                        citations.append(citation)
            
            # Remove duplicates based on source_id
            unique_citations = {}
            for citation in citations:
                if citation.source_id not in unique_citations:
                    unique_citations[citation.source_id] = citation
            
            return list(unique_citations.values())
            
        except Exception as e:
            logger.error(f"Error extracting citations: {e}", exc_info=True)
            return []
    
    def _parse_citation_match(self, citation_type: str, match: re.Match) -> Optional[Citation]:
        """Parse a citation match based on its type."""
        try:
            if citation_type == "standard":
                title = match.group(1).strip()
                return Citation(
                    source_id=self._generate_source_id(title),
                    title=title,
                    citation_text=match.group(0)
                )
            
            elif citation_type == "legal_section":
                section = match.group(1)
                law = match.group(2)
                title = f"{law} § {section}"
                return Citation(
                    source_id=self._generate_source_id(title),
                    title=title,
                    section=section,
                    citation_text=match.group(0)
                )
            
            elif citation_type == "case":
                court = match.group(1)
                case_type = match.group(2)
                case_ref = match.group(3)
                title = f"{court} {case_type} {case_ref}"
                return Citation(
                    source_id=self._generate_source_id(title),
                    title=title,
                    citation_text=match.group(0)
                )
            
            elif citation_type == "url":
                url = match.group(1)
                return Citation(
                    source_id=self._generate_source_id(url),
                    title=url,
                    url=url,
                    citation_text=match.group(0)
                )
            
            elif citation_type == "page":
                title = match.group(1).strip()
                page = match.group(2)
                return Citation(
                    source_id=self._generate_source_id(title),
                    title=title,
                    page=page,
                    citation_text=match.group(0)
                )
            
            elif citation_type == "section":
                title = match.group(1).strip()
                section = match.group(2)
                return Citation(
                    source_id=self._generate_source_id(title),
                    title=title,
                    section=section,
                    citation_text=match.group(0)
                )
            
            return None
            
        except Exception as e:
            logger.warning(f"Error parsing citation match: {e}")
            return None
    
    def _generate_source_id(self, title: str) -> str:
        """Generate a source ID from title."""
        # Simple hash-based ID generation
        import hashlib
        return hashlib.md5(title.lower().encode()).hexdigest()[:12]
    
    def validate_citations(self, 
                          citations: List[Citation], 
                          source_results: List[FusedResult]) -> List[Citation]:
        """
        Validate citations against source documents.
        
        Args:
            citations: Extracted citations
            source_results: Original retrieval results
            
        Returns:
            List of validated citations with additional metadata
        """
        try:
            validated_citations = []
            
            # Create lookup for source results
            source_lookup = {}
            for result in source_results:
                title = result.chunk.metadata.get("title", "Unknown Document")
                source_lookup[self._generate_source_id(title)] = result
            
            for citation in citations:
                # Try to match with source results
                if citation.source_id in source_lookup:
                    result = source_lookup[citation.source_id]
                    
                    # Enhance citation with source metadata
                    citation.url = result.chunk.source_url
                    citation.excerpt = self._extract_relevant_excerpt(
                        citation, result.chunk.content
                    )
                    
                    # Add metadata from chunk
                    if hasattr(citation, 'metadata'):
                        citation.metadata = result.chunk.metadata
                    
                    validated_citations.append(citation)
                else:
                    # Citation not found in sources - mark as unvalidated
                    logger.warning(f"Citation not found in sources: {citation.title}")
                    validated_citations.append(citation)
            
            return validated_citations
            
        except Exception as e:
            logger.error(f"Error validating citations: {e}", exc_info=True)
            return citations
    
    def _extract_relevant_excerpt(self, citation: Citation, content: str) -> str:
        """Extract a relevant excerpt from the content for the citation."""
        try:
            # Simple excerpt extraction - first 200 characters
            excerpt = content[:200].strip()
            if len(content) > 200:
                excerpt += "..."
            
            return excerpt
            
        except Exception as e:
            logger.warning(f"Error extracting excerpt: {e}")
            return ""
    
    def format_citations_for_response(self, citations: List[Citation]) -> List[Dict[str, Any]]:
        """
        Format citations for inclusion in API response.
        
        Args:
            citations: List of citations to format
            
        Returns:
            List of formatted citation dictionaries
        """
        try:
            formatted_citations = []
            
            for citation in citations:
                formatted_citation = {
                    "title": citation.title,
                    "url": citation.url,
                    "excerpt": citation.excerpt
                }
                
                # Add optional fields if available
                if citation.page:
                    formatted_citation["page"] = citation.page
                
                if citation.section:
                    formatted_citation["section"] = citation.section
                
                formatted_citations.append(formatted_citation)
            
            return formatted_citations
            
        except Exception as e:
            logger.error(f"Error formatting citations: {e}", exc_info=True)
            return []
    
    def enhance_response_with_citations(self, 
                                      response_text: str, 
                                      citations: List[Citation]) -> str:
        """
        Enhance response text with properly formatted citations.
        
        Args:
            response_text: Original response text
            citations: List of validated citations
            
        Returns:
            Enhanced response text with formatted citations
        """
        try:
            enhanced_text = response_text
            
            # Replace citation placeholders with formatted citations
            for citation in citations:
                if citation.citation_text:
                    # Create enhanced citation format
                    enhanced_citation = self._format_enhanced_citation(citation)
                    enhanced_text = enhanced_text.replace(
                        citation.citation_text, 
                        enhanced_citation
                    )
            
            return enhanced_text
            
        except Exception as e:
            logger.error(f"Error enhancing response with citations: {e}", exc_info=True)
            return response_text
    
    def _format_enhanced_citation(self, citation: Citation) -> str:
        """Format an enhanced citation for display."""
        try:
            # Basic format
            formatted = f"[{citation.title}"
            
            # Add section if available
            if citation.section:
                formatted += f", § {citation.section}"
            
            # Add page if available
            if citation.page:
                formatted += f", p. {citation.page}"
            
            formatted += "]"
            
            return formatted
            
        except Exception as e:
            logger.warning(f"Error formatting enhanced citation: {e}")
            return citation.citation_text or f"[{citation.title}]"
    
    def get_citation_stats(self, citations: List[Citation]) -> Dict[str, Any]:
        """Get statistics about citations."""
        if not citations:
            return {}
        
        citation_types = {}
        has_url_count = 0
        has_excerpt_count = 0
        
        for citation in citations:
            # Count by type (based on citation text pattern)
            if citation.citation_text:
                if "§" in citation.citation_text:
                    citation_types["legal_section"] = citation_types.get("legal_section", 0) + 1
                elif "Source:" in citation.citation_text:
                    citation_types["standard"] = citation_types.get("standard", 0) + 1
                elif "http" in citation.citation_text:
                    citation_types["url"] = citation_types.get("url", 0) + 1
                else:
                    citation_types["other"] = citation_types.get("other", 0) + 1
            
            if citation.url:
                has_url_count += 1
            
            if citation.excerpt:
                has_excerpt_count += 1
        
        return {
            "total_citations": len(citations),
            "citation_types": citation_types,
            "citations_with_url": has_url_count,
            "citations_with_excerpt": has_excerpt_count,
            "validation_rate": (has_url_count / len(citations)) * 100 if citations else 0
        }
    
    def clean_response_citations(self, response_text: str) -> str:
        """Clean up malformed or duplicate citations in response text."""
        try:
            # Remove duplicate citations
            seen_citations = set()
            lines = response_text.split('\n')
            cleaned_lines = []
            
            for line in lines:
                # Find citations in line
                citations_in_line = []
                for pattern in self._citation_patterns.values():
                    matches = re.findall(pattern, line, re.IGNORECASE)
                    citations_in_line.extend(matches)
                
                # Remove duplicates
                for citation in citations_in_line:
                    citation_key = str(citation).lower()
                    if citation_key in seen_citations:
                        # Remove duplicate citation from line
                        for pattern in self._citation_patterns.values():
                            line = re.sub(pattern, '', line, count=1, flags=re.IGNORECASE)
                    else:
                        seen_citations.add(citation_key)
                
                cleaned_lines.append(line)
            
            return '\n'.join(cleaned_lines)
            
        except Exception as e:
            logger.error(f"Error cleaning response citations: {e}", exc_info=True)
            return response_text
