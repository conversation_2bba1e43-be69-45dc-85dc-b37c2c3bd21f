"""
Response generation using Google Gemini LLM.

This module handles the integration with Google Gemini 1.5 Flash for
generating legal responses with proper context, citations, and streaming support.
"""

import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.callbacks import AsyncCallbackHandler

from ...shared.config import get_settings
from ...shared.logging_config import get_logger
from ...shared.models import ProcessedQuery, QueryResponse
from ..retrieval.result_fusion import FusedResult
from .prompt_manager import PromptManager
from .citation_manager import CitationManager, Citation

logger = get_logger(__name__)


class StreamingCallbackHandler(AsyncCallbackHandler):
    """Callback handler for streaming responses."""
    
    def __init__(self):
        self.tokens = []
        self.is_streaming = False
    
    async def on_llm_new_token(self, token: str, **kwargs) -> None:
        """Handle new token from LLM."""
        self.tokens.append(token)
        self.is_streaming = True
    
    def get_response(self) -> str:
        """Get the complete response."""
        return "".join(self.tokens)
    
    def reset(self):
        """Reset the handler for new response."""
        self.tokens = []
        self.is_streaming = False


class ResponseGenerator:
    """
    Generates responses using Google Gemini LLM with RAG context.
    
    This class handles the complete response generation pipeline including
    prompt preparation, LLM interaction, citation extraction, and streaming.
    """
    
    def __init__(self):
        """Initialize the response generator."""
        self.settings = get_settings()
        self.prompt_manager = PromptManager()
        self.citation_manager = CitationManager()
        
        # Initialize LLM
        self.llm = None
        self._initialized = False
        
        # Configuration
        self.model_name = getattr(self.settings, 'gemini_model', 'gemini-1.5-flash')
        self.temperature = getattr(self.settings, 'llm_temperature', 0.1)
        self.max_tokens = getattr(self.settings, 'max_response_tokens', 2048)
        self.top_p = getattr(self.settings, 'llm_top_p', 0.8)
        
    async def initialize(self):
        """Initialize the LLM and components."""
        if self._initialized:
            return
            
        try:
            logger.info("Initializing response generator...")
            
            # Initialize Google Gemini LLM
            self.llm = ChatGoogleGenerativeAI(
                model=self.model_name,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                top_p=self.top_p,
                google_api_key=self.settings.google_api_key
            )
            
            self._initialized = True
            logger.info(f"Response generator initialized with model: {self.model_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize response generator: {e}", exc_info=True)
            raise
    
    async def generate_response(self,
                               processed_query: ProcessedQuery,
                               retrieved_results: List[FusedResult],
                               conversation_history: Optional[List[Dict[str, Any]]] = None,
                               session_id: Optional[str] = None) -> QueryResponse:
        """
        Generate a complete response for the query.
        
        Args:
            processed_query: The processed query with analysis
            retrieved_results: Results from hybrid retrieval
            conversation_history: Previous conversation context
            session_id: Session identifier
            
        Returns:
            Complete query response with answer and citations
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            start_time = datetime.utcnow()
            
            logger.info(f"Generating response for query: {processed_query.sanitized_query[:100]}...")
            
            # Create optimized prompt
            prompt_data = self.prompt_manager.create_prompt(
                processed_query=processed_query,
                retrieved_results=retrieved_results,
                conversation_history=conversation_history
            )
            
            # Generate response using LLM
            response_text = await self._generate_llm_response(
                system_prompt=prompt_data["system_prompt"],
                user_prompt=prompt_data["user_prompt"]
            )
            
            # Extract and validate citations
            citations = self.citation_manager.extract_citations(response_text)
            validated_citations = self.citation_manager.validate_citations(
                citations, retrieved_results
            )
            
            # Enhance response with proper citations
            enhanced_response = self.citation_manager.enhance_response_with_citations(
                response_text, validated_citations
            )
            
            # Format citations for API response
            formatted_citations = self.citation_manager.format_citations_for_response(
                validated_citations
            )
            
            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Create response object
            response = QueryResponse(
                answer=enhanced_response,
                sources=formatted_citations,
                session_id=session_id or processed_query.session_id,
                processing_time=processing_time,
                metadata={
                    "model": self.model_name,
                    "prompt_type": prompt_data["prompt_type"],
                    "context_length": prompt_data["context_length"],
                    "source_count": prompt_data["source_count"],
                    "citation_count": len(validated_citations),
                    "query_type": processed_query.query_type,
                    "query_complexity": processed_query.complexity,
                    "retrieval_methods": list(set(
                        method for result in retrieved_results 
                        for method in result.retrieval_methods
                    )),
                    "generation_time": processing_time,
                    "timestamp": start_time.isoformat()
                }
            )
            
            logger.info(f"Response generated successfully in {processing_time:.3f}s")
            return response
            
        except Exception as e:
            logger.error(f"Error generating response: {e}", exc_info=True)
            raise
    
    async def generate_streaming_response(self,
                                        processed_query: ProcessedQuery,
                                        retrieved_results: List[FusedResult],
                                        conversation_history: Optional[List[Dict[str, Any]]] = None,
                                        session_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response for real-time delivery.
        
        Args:
            processed_query: The processed query with analysis
            retrieved_results: Results from hybrid retrieval
            conversation_history: Previous conversation context
            session_id: Session identifier
            
        Yields:
            Response chunks as they are generated
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            logger.info(f"Starting streaming response for query: {processed_query.sanitized_query[:100]}...")
            
            # Create optimized prompt
            prompt_data = self.prompt_manager.create_prompt(
                processed_query=processed_query,
                retrieved_results=retrieved_results,
                conversation_history=conversation_history
            )
            
            # Set up streaming callback
            callback_handler = StreamingCallbackHandler()
            
            # Create messages
            messages = [
                SystemMessage(content=prompt_data["system_prompt"]),
                HumanMessage(content=prompt_data["user_prompt"])
            ]
            
            # Stream response
            async for chunk in self.llm.astream(messages, callbacks=[callback_handler]):
                if hasattr(chunk, 'content') and chunk.content:
                    yield chunk.content
            
            # After streaming is complete, handle citations
            complete_response = callback_handler.get_response()
            
            # Extract and validate citations (done in background)
            asyncio.create_task(self._process_citations_async(
                complete_response, retrieved_results, session_id
            ))
            
        except Exception as e:
            logger.error(f"Error in streaming response: {e}", exc_info=True)
            yield f"\n\n[Error: {str(e)}]"
    
    async def _generate_llm_response(self, system_prompt: str, user_prompt: str) -> str:
        """Generate response using the LLM."""
        try:
            # Create messages
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # Generate response
            response = await self.llm.ainvoke(messages)
            
            return response.content
            
        except Exception as e:
            logger.error(f"Error generating LLM response: {e}", exc_info=True)
            raise
    
    async def _process_citations_async(self,
                                     response_text: str,
                                     retrieved_results: List[FusedResult],
                                     session_id: Optional[str]):
        """Process citations asynchronously for streaming responses."""
        try:
            # Extract and validate citations
            citations = self.citation_manager.extract_citations(response_text)
            validated_citations = self.citation_manager.validate_citations(
                citations, retrieved_results
            )
            
            # Store citations for later retrieval (could be stored in Redis/database)
            # This is a placeholder for citation storage logic
            logger.info(f"Processed {len(validated_citations)} citations for session {session_id}")
            
        except Exception as e:
            logger.error(f"Error processing citations asynchronously: {e}", exc_info=True)
    
    async def regenerate_response(self,
                                 processed_query: ProcessedQuery,
                                 retrieved_results: List[FusedResult],
                                 feedback: Optional[str] = None,
                                 session_id: Optional[str] = None) -> QueryResponse:
        """
        Regenerate response with optional feedback.
        
        Args:
            processed_query: The processed query
            retrieved_results: Retrieval results
            feedback: User feedback for improvement
            session_id: Session identifier
            
        Returns:
            Regenerated response
        """
        try:
            # Modify prompt based on feedback
            if feedback:
                # Add feedback to the query context
                modified_query = processed_query.copy()
                modified_query.sanitized_query += f"\n\nUser feedback: {feedback}"
                
                return await self.generate_response(
                    processed_query=modified_query,
                    retrieved_results=retrieved_results,
                    session_id=session_id
                )
            else:
                # Simple regeneration with slightly different parameters
                original_temp = self.llm.temperature
                self.llm.temperature = min(original_temp + 0.1, 1.0)
                
                try:
                    response = await self.generate_response(
                        processed_query=processed_query,
                        retrieved_results=retrieved_results,
                        session_id=session_id
                    )
                    return response
                finally:
                    # Restore original temperature
                    self.llm.temperature = original_temp
                    
        except Exception as e:
            logger.error(f"Error regenerating response: {e}", exc_info=True)
            raise
    
    def configure_model(self,
                       temperature: Optional[float] = None,
                       max_tokens: Optional[int] = None,
                       top_p: Optional[float] = None):
        """Configure model parameters."""
        if temperature is not None:
            self.temperature = temperature
            if self.llm:
                self.llm.temperature = temperature
        
        if max_tokens is not None:
            self.max_tokens = max_tokens
            if self.llm:
                self.llm.max_tokens = max_tokens
        
        if top_p is not None:
            self.top_p = top_p
            if self.llm:
                self.llm.top_p = top_p
        
        logger.info(f"Model configured: temp={self.temperature}, max_tokens={self.max_tokens}, top_p={self.top_p}")
    
    async def get_generation_stats(self) -> Dict[str, Any]:
        """Get response generation statistics."""
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
            "initialized": self._initialized
        }
    
    async def health_check(self) -> bool:
        """Check if the response generator is healthy."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test basic functionality
            test_messages = [
                SystemMessage(content="You are a helpful assistant."),
                HumanMessage(content="Say 'OK' if you can respond.")
            ]
            
            response = await self.llm.ainvoke(test_messages)
            return "OK" in response.content.upper()
            
        except Exception as e:
            logger.error(f"Response generator health check failed: {e}")
            return False
    
    async def close(self):
        """Close connections and cleanup resources."""
        try:
            # Cleanup LLM resources if needed
            self.llm = None
            self._initialized = False
            
            logger.info("Response generator closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing response generator: {e}", exc_info=True)
