"""
Prompt management for LLM interactions.

This module handles prompt templates, context preparation, and prompt
optimization for different types of legal queries and responses.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum

from ...shared.logging_config import get_logger
from ...shared.models import ProcessedQuery
from ..retrieval.result_fusion import FusedResult

logger = get_logger(__name__)


class PromptType(Enum):
    """Types of prompts for different scenarios."""
    LEGAL_QUESTION = "legal_question"
    DEFINITION_REQUEST = "definition_request"
    CASE_LOOKUP = "case_lookup"
    STATUTE_LOOKUP = "statute_lookup"
    PROCEDURAL_QUESTION = "procedural_question"
    GENERAL_LEGAL = "general_legal"
    FOLLOW_UP = "follow_up"


class PromptManager:
    """
    Manages prompt templates and context preparation for LLM interactions.
    
    This class handles the creation of optimized prompts based on query type,
    context preparation from retrieved documents, and citation formatting.
    """
    
    def __init__(self):
        """Initialize the prompt manager."""
        self.logger = get_logger(__name__)
        self._prompt_templates = self._load_prompt_templates()
        self._system_prompts = self._load_system_prompts()
        
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load prompt templates for different query types."""
        return {
            PromptType.LEGAL_QUESTION.value: """
Based on the following legal documents and context, please provide a comprehensive answer to the user's question.

**User Question:** {query}

**Relevant Legal Context:**
{context}

**Instructions:**
1. Provide a clear, accurate, and comprehensive answer based on the provided context
2. Reference specific legal provisions, cases, or documents when applicable
3. Include proper citations in the format [Source: {source_title}]
4. If the context doesn't fully address the question, clearly state the limitations
5. Use clear, professional language appropriate for legal consultation
6. Structure your response with clear headings if the topic is complex

**Answer:**
""",
            
            PromptType.DEFINITION_REQUEST.value: """
Based on the provided legal documents, please define and explain the following legal term or concept.

**Term/Concept to Define:** {query}

**Relevant Legal Context:**
{context}

**Instructions:**
1. Provide a clear, precise definition of the term/concept
2. Explain the legal significance and application
3. Include relevant examples or use cases if available in the context
4. Reference the specific legal sources for the definition [Source: {source_title}]
5. If there are different interpretations or applications, explain them clearly

**Definition and Explanation:**
""",
            
            PromptType.CASE_LOOKUP.value: """
Based on the legal documents provided, please provide information about the requested case or legal precedent.

**Case/Precedent Query:** {query}

**Relevant Legal Context:**
{context}

**Instructions:**
1. Summarize the key facts and legal issues of the case
2. Explain the court's decision and reasoning
3. Discuss the legal precedent established and its significance
4. Include proper citations [Source: {source_title}]
5. If multiple related cases are found, compare and contrast them

**Case Information:**
""",
            
            PromptType.STATUTE_LOOKUP.value: """
Based on the legal documents provided, please explain the requested statute, law, or regulation.

**Statute/Law Query:** {query}

**Relevant Legal Context:**
{context}

**Instructions:**
1. Provide the exact text of the relevant statute or regulation if available
2. Explain the purpose and scope of the law
3. Discuss key provisions and their practical implications
4. Include proper citations [Source: {source_title}]
5. Explain how this law relates to other relevant legal provisions

**Statute/Law Explanation:**
""",
            
            PromptType.PROCEDURAL_QUESTION.value: """
Based on the legal documents provided, please explain the legal procedure or process requested.

**Procedural Question:** {query}

**Relevant Legal Context:**
{context}

**Instructions:**
1. Provide a step-by-step explanation of the legal procedure
2. Include required documents, deadlines, and key considerations
3. Explain the roles of different parties involved
4. Reference specific procedural rules or requirements [Source: {source_title}]
5. Highlight important deadlines or critical steps

**Procedural Explanation:**
""",
            
            PromptType.GENERAL_LEGAL.value: """
Based on the provided legal documents and context, please address the user's legal inquiry.

**Legal Inquiry:** {query}

**Relevant Legal Context:**
{context}

**Instructions:**
1. Provide a helpful and accurate response based on the available context
2. Structure your answer clearly and logically
3. Include relevant legal principles and authorities
4. Use proper citations [Source: {source_title}]
5. If the question requires specialized expertise beyond the provided context, recommend consulting with a qualified legal professional

**Response:**
""",
            
            PromptType.FOLLOW_UP.value: """
Based on the conversation history and new legal documents, please address this follow-up question.

**Previous Context:** {conversation_history}

**Follow-up Question:** {query}

**Additional Legal Context:**
{context}

**Instructions:**
1. Consider the previous conversation context in your response
2. Build upon previous answers while addressing the new question
3. Provide additional relevant information from the new context
4. Include proper citations [Source: {source_title}]
5. Maintain consistency with previous responses

**Follow-up Response:**
"""
        }
    
    def _load_system_prompts(self) -> Dict[str, str]:
        """Load system prompts for different scenarios."""
        return {
            "default": """You are a knowledgeable legal assistant specializing in German law. Your role is to provide accurate, helpful, and well-researched legal information based on authoritative legal sources.

**Your Capabilities:**
- Analyze legal documents and provide comprehensive answers
- Explain complex legal concepts in clear, understandable language
- Reference specific legal provisions, cases, and authorities
- Provide structured, professional responses

**Important Guidelines:**
- Always base your responses on the provided legal context and documents
- Include proper citations for all legal references
- If information is incomplete or unclear, acknowledge these limitations
- Recommend consulting with qualified legal professionals for specific legal advice
- Use professional, clear language appropriate for legal consultation
- Structure complex responses with clear headings and bullet points

**Response Format:**
- Provide direct, actionable answers when possible
- Use proper legal citation format: [Source: Document Title]
- Include relevant legal principles and their applications
- Highlight important deadlines, requirements, or considerations""",
            
            "conversational": """You are a helpful legal assistant engaged in an ongoing conversation about German law. You have access to comprehensive legal documents and can provide detailed, accurate information.

**Conversation Guidelines:**
- Maintain context from previous exchanges
- Build upon previous answers when addressing follow-up questions
- Acknowledge when you're expanding on or clarifying previous responses
- Keep responses focused and relevant to the current question
- Use a professional but approachable tone

**Quality Standards:**
- Ensure accuracy and consistency across all responses
- Provide proper citations for all legal references
- Acknowledge limitations in available information
- Recommend professional legal consultation when appropriate"""
        }
    
    def create_prompt(self,
                     processed_query: ProcessedQuery,
                     retrieved_results: List[FusedResult],
                     conversation_history: Optional[List[Dict[str, Any]]] = None) -> Dict[str, str]:
        """
        Create an optimized prompt for the LLM based on query type and context.
        
        Args:
            processed_query: The processed query with analysis
            retrieved_results: Results from hybrid retrieval
            conversation_history: Previous conversation context
            
        Returns:
            Dictionary with system_prompt and user_prompt
        """
        try:
            # Determine prompt type
            prompt_type = self._determine_prompt_type(processed_query)
            
            # Prepare context from retrieved results
            context = self._prepare_context(retrieved_results)
            
            # Get appropriate template
            template = self._prompt_templates.get(prompt_type.value, self._prompt_templates[PromptType.GENERAL_LEGAL.value])
            
            # Prepare template variables
            template_vars = {
                "query": processed_query.sanitized_query,
                "context": context
            }
            
            # Add conversation history if available
            if conversation_history and prompt_type == PromptType.FOLLOW_UP:
                template_vars["conversation_history"] = self._format_conversation_history(conversation_history)
            
            # Format the prompt
            user_prompt = template.format(**template_vars)
            
            # Select system prompt
            system_prompt_key = "conversational" if conversation_history else "default"
            system_prompt = self._system_prompts[system_prompt_key]
            
            return {
                "system_prompt": system_prompt,
                "user_prompt": user_prompt,
                "prompt_type": prompt_type.value,
                "context_length": len(context),
                "source_count": len(retrieved_results)
            }
            
        except Exception as e:
            logger.error(f"Error creating prompt: {e}", exc_info=True)
            raise
    
    def _determine_prompt_type(self, processed_query: ProcessedQuery) -> PromptType:
        """Determine the appropriate prompt type based on query analysis."""
        query_type = processed_query.query_type
        
        # Map query types to prompt types
        type_mapping = {
            "definition_request": PromptType.DEFINITION_REQUEST,
            "case_lookup": PromptType.CASE_LOOKUP,
            "statute_lookup": PromptType.STATUTE_LOOKUP,
            "procedural_question": PromptType.PROCEDURAL_QUESTION,
            "legal_question": PromptType.LEGAL_QUESTION,
            "general_legal": PromptType.GENERAL_LEGAL
        }
        
        return type_mapping.get(query_type, PromptType.GENERAL_LEGAL)
    
    def _prepare_context(self, retrieved_results: List[FusedResult]) -> str:
        """Prepare context from retrieved results for the prompt."""
        if not retrieved_results:
            return "No relevant legal documents found for this query."
        
        context_parts = []
        
        for i, result in enumerate(retrieved_results, 1):
            # Extract source information
            source_title = result.chunk.metadata.get("title", "Unknown Document")
            source_url = result.chunk.source_url or "No URL available"
            
            # Format the context entry
            context_entry = f"""
**Document {i}: {source_title}**
Source: {source_url}
Relevance Score: {result.final_score:.3f}
Retrieval Methods: {', '.join(result.retrieval_methods)}

Content:
{result.chunk.content}

---
"""
            context_parts.append(context_entry)
        
        return "\n".join(context_parts)
    
    def _format_conversation_history(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Format conversation history for inclusion in prompts."""
        if not conversation_history:
            return "No previous conversation context."
        
        history_parts = []
        
        for i, exchange in enumerate(conversation_history[-3:], 1):  # Last 3 exchanges
            history_entry = f"""
**Exchange {i}:**
User: {exchange.get('query', 'Unknown query')}
Assistant: {exchange.get('response', 'Unknown response')[:500]}...

---
"""
            history_parts.append(history_entry)
        
        return "\n".join(history_parts)
    
    def optimize_prompt_length(self, prompt: str, max_tokens: int = 4000) -> str:
        """Optimize prompt length to fit within token limits."""
        # Simple token estimation (rough approximation)
        estimated_tokens = len(prompt.split()) * 1.3
        
        if estimated_tokens <= max_tokens:
            return prompt
        
        # If too long, truncate context while preserving structure
        lines = prompt.split('\n')
        
        # Keep system instructions and query, truncate context
        system_lines = []
        context_lines = []
        in_context = False
        
        for line in lines:
            if "**Relevant Legal Context:**" in line:
                in_context = True
            
            if in_context and "**Instructions:**" in line:
                in_context = False
            
            if in_context:
                context_lines.append(line)
            else:
                system_lines.append(line)
        
        # Truncate context if needed
        if context_lines:
            target_context_tokens = max_tokens - len(' '.join(system_lines).split()) * 1.3
            context_text = '\n'.join(context_lines)
            
            if len(context_text.split()) * 1.3 > target_context_tokens:
                # Truncate context
                words = context_text.split()
                target_words = int(target_context_tokens / 1.3)
                truncated_context = ' '.join(words[:target_words]) + "\n\n[Context truncated due to length...]"
                
                # Replace context in original prompt
                context_start = prompt.find("**Relevant Legal Context:**")
                context_end = prompt.find("**Instructions:**")
                
                if context_start != -1 and context_end != -1:
                    prompt = (prompt[:context_start] + 
                             "**Relevant Legal Context:**\n" + 
                             truncated_context + "\n\n" +
                             prompt[context_end:])
        
        return prompt
    
    def get_prompt_stats(self, prompt: str) -> Dict[str, Any]:
        """Get statistics about a prompt."""
        return {
            "character_count": len(prompt),
            "word_count": len(prompt.split()),
            "estimated_tokens": len(prompt.split()) * 1.3,
            "line_count": len(prompt.split('\n')),
            "has_context": "**Relevant Legal Context:**" in prompt,
            "has_instructions": "**Instructions:**" in prompt
        }
