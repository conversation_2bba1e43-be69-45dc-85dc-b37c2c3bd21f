"""
Hybrid retrieval system combining vector and keyword search.

This module orchestrates multiple retrieval methods to provide the best
possible search results by combining semantic similarity (vector search)
with exact term matching (keyword search).
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from enum import Enum

from ...shared.config import get_settings
from ...shared.logging_config import get_logger
from ...shared.models import ProcessedQuery
from .vector_retriever import VectorRetriever, RetrievalResult
from .keyword_retriever import KeywordRetriever, KeywordRetrievalResult
from .result_fusion import ResultFusion, FusionStrategy, FusedResult

logger = get_logger(__name__)


class RetrievalMode(Enum):
    """Available retrieval modes."""
    HYBRID = "hybrid"           # Both vector and keyword search
    VECTOR_ONLY = "vector_only" # Only vector search
    KEYWORD_ONLY = "keyword_only" # Only keyword search
    ADAPTIVE = "adaptive"       # Automatically choose based on query


class HybridRetriever:
    """
    Hybrid retrieval system that combines multiple search methods.
    
    This class orchestrates vector similarity search and keyword-based search
    to provide comprehensive retrieval capabilities. It uses result fusion
    techniques to combine and rank results from different methods.
    """
    
    def __init__(self, 
                 fusion_strategy: FusionStrategy = FusionStrategy.RRF,
                 default_mode: RetrievalMode = RetrievalMode.HYBRID):
        """
        Initialize the hybrid retriever.
        
        Args:
            fusion_strategy: Strategy for combining results from different methods
            default_mode: Default retrieval mode
        """
        self.settings = get_settings()
        self.fusion_strategy = fusion_strategy
        self.default_mode = default_mode
        
        # Initialize retrieval components
        self.vector_retriever = VectorRetriever()
        self.keyword_retriever = KeywordRetriever()
        self.result_fusion = ResultFusion(fusion_strategy)
        
        self._initialized = False
        
        # Configuration
        self.vector_weight = getattr(self.settings, 'vector_weight', 0.6)
        self.keyword_weight = getattr(self.settings, 'keyword_weight', 0.4)
        self.min_vector_score = getattr(self.settings, 'min_vector_score', 0.7)
        self.min_keyword_score = getattr(self.settings, 'min_keyword_score', 0.0)
        
    async def initialize(self):
        """Initialize all retrieval components."""
        if self._initialized:
            return
            
        try:
            logger.info("Initializing hybrid retriever...")
            
            # Initialize components in parallel
            await asyncio.gather(
                self.vector_retriever.initialize(),
                self.keyword_retriever.initialize()
            )
            
            self._initialized = True
            logger.info("Hybrid retriever initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize hybrid retriever: {e}", exc_info=True)
            raise
    
    async def retrieve(self,
                      processed_query: ProcessedQuery,
                      top_k: int = 10,
                      mode: Optional[RetrievalMode] = None) -> List[FusedResult]:
        """
        Retrieve relevant documents using hybrid search.
        
        Args:
            processed_query: The processed query with analysis
            top_k: Number of top results to return
            mode: Retrieval mode (defaults to instance default)
            
        Returns:
            List of fused results sorted by relevance
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            start_time = datetime.utcnow()
            
            # Determine retrieval mode
            retrieval_mode = mode or self._determine_optimal_mode(processed_query)
            
            logger.info(f"Starting hybrid retrieval in {retrieval_mode.value} mode for query: {processed_query.sanitized_query[:100]}...")
            
            # Execute retrieval based on mode
            if retrieval_mode == RetrievalMode.VECTOR_ONLY:
                return await self._vector_only_retrieval(processed_query, top_k)
            elif retrieval_mode == RetrievalMode.KEYWORD_ONLY:
                return await self._keyword_only_retrieval(processed_query, top_k)
            else:  # HYBRID or ADAPTIVE
                return await self._hybrid_retrieval(processed_query, top_k)
                
        except Exception as e:
            logger.error(f"Error in hybrid retrieval: {e}", exc_info=True)
            raise
    
    async def _hybrid_retrieval(self,
                               processed_query: ProcessedQuery,
                               top_k: int) -> List[FusedResult]:
        """Execute hybrid retrieval combining vector and keyword search."""
        try:
            # Calculate retrieval counts for each method
            vector_k = min(top_k * 2, 50)  # Get more candidates for fusion
            keyword_k = min(top_k * 2, 50)
            
            # Execute both retrieval methods in parallel
            vector_task = self.vector_retriever.retrieve(
                processed_query, 
                top_k=vector_k,
                score_threshold=self.min_vector_score
            )
            
            keyword_task = self.keyword_retriever.retrieve(
                processed_query,
                top_k=keyword_k,
                score_threshold=self.min_keyword_score
            )
            
            vector_results, keyword_results = await asyncio.gather(vector_task, keyword_task)
            
            # Log retrieval statistics
            logger.info(f"Retrieved {len(vector_results)} vector results and {len(keyword_results)} keyword results")
            
            # Fuse results
            fused_results = self.result_fusion.fuse_results(
                vector_results=vector_results,
                keyword_results=keyword_results,
                top_k=top_k,
                vector_weight=self.vector_weight,
                keyword_weight=self.keyword_weight
            )
            
            return fused_results
            
        except Exception as e:
            logger.error(f"Error in hybrid retrieval execution: {e}", exc_info=True)
            raise
    
    async def _vector_only_retrieval(self,
                                    processed_query: ProcessedQuery,
                                    top_k: int) -> List[FusedResult]:
        """Execute vector-only retrieval."""
        try:
            vector_results = await self.vector_retriever.retrieve(
                processed_query,
                top_k=top_k,
                score_threshold=self.min_vector_score
            )
            
            # Convert to fused results
            fused_results = []
            for i, result in enumerate(vector_results):
                fused_result = FusedResult(
                    chunk=result.chunk,
                    final_score=result.score,
                    rank=i + 1
                )
                fused_result.add_component_result(
                    method="vector",
                    score=result.score,
                    rank=result.rank,
                    matched_terms=[]
                )
                fused_results.append(fused_result)
            
            return fused_results
            
        except Exception as e:
            logger.error(f"Error in vector-only retrieval: {e}", exc_info=True)
            raise
    
    async def _keyword_only_retrieval(self,
                                     processed_query: ProcessedQuery,
                                     top_k: int) -> List[FusedResult]:
        """Execute keyword-only retrieval."""
        try:
            keyword_results = await self.keyword_retriever.retrieve(
                processed_query,
                top_k=top_k,
                score_threshold=self.min_keyword_score
            )
            
            # Convert to fused results
            fused_results = []
            for i, result in enumerate(keyword_results):
                fused_result = FusedResult(
                    chunk=result.chunk,
                    final_score=result.score,
                    rank=i + 1
                )
                fused_result.add_component_result(
                    method="keyword",
                    score=result.score,
                    rank=result.rank,
                    matched_terms=result.matched_terms
                )
                fused_results.append(fused_result)
            
            return fused_results
            
        except Exception as e:
            logger.error(f"Error in keyword-only retrieval: {e}", exc_info=True)
            raise
    
    def _determine_optimal_mode(self, processed_query: ProcessedQuery) -> RetrievalMode:
        """Determine the optimal retrieval mode based on query characteristics."""
        if self.default_mode != RetrievalMode.ADAPTIVE:
            return self.default_mode
        
        # Analyze query characteristics
        query_type = processed_query.query_type
        complexity = processed_query.complexity
        key_terms_count = len(processed_query.key_terms)
        has_entities = any(processed_query.entities.values())
        
        # Decision logic for adaptive mode
        if query_type in ["definition_request", "statute_lookup"] and has_entities:
            # Exact lookups benefit from keyword search
            return RetrievalMode.KEYWORD_ONLY
        elif query_type in ["legal_question", "general_legal"] and complexity == "complex":
            # Complex legal questions benefit from semantic search
            return RetrievalMode.VECTOR_ONLY
        elif key_terms_count > 5:
            # Many key terms suggest keyword search might be effective
            return RetrievalMode.HYBRID
        else:
            # Default to hybrid for balanced results
            return RetrievalMode.HYBRID
    
    async def search_similar_to_chunk(self,
                                     chunk_id: str,
                                     top_k: int = 5) -> List[FusedResult]:
        """Find documents similar to a given chunk."""
        if not self._initialized:
            await self.initialize()
            
        try:
            # Use vector similarity for finding similar chunks
            vector_results = await self.vector_retriever.retrieve_similar_chunks(chunk_id, top_k)
            
            # Convert to fused results
            fused_results = []
            for i, result in enumerate(vector_results):
                fused_result = FusedResult(
                    chunk=result.chunk,
                    final_score=result.score,
                    rank=i + 1
                )
                fused_result.add_component_result(
                    method="vector_similarity",
                    score=result.score,
                    rank=result.rank,
                    matched_terms=[]
                )
                fused_results.append(fused_result)
            
            return fused_results
            
        except Exception as e:
            logger.error(f"Error finding similar chunks: {e}", exc_info=True)
            raise
    
    async def refresh_indexes(self):
        """Refresh all search indexes."""
        try:
            logger.info("Refreshing hybrid retriever indexes...")
            
            # Refresh indexes in parallel
            await asyncio.gather(
                self.keyword_retriever.refresh_index(),
                # Vector store refresh would be handled by the offline pipeline
            )
            
            logger.info("Hybrid retriever indexes refreshed successfully")
            
        except Exception as e:
            logger.error(f"Error refreshing indexes: {e}", exc_info=True)
            raise
    
    def configure_weights(self, vector_weight: float, keyword_weight: float):
        """Configure the weights for different retrieval methods."""
        total = vector_weight + keyword_weight
        if total > 0:
            self.vector_weight = vector_weight / total
            self.keyword_weight = keyword_weight / total
        else:
            self.vector_weight = 0.5
            self.keyword_weight = 0.5
        
        logger.info(f"Updated retrieval weights: vector={self.vector_weight:.2f}, keyword={self.keyword_weight:.2f}")
    
    def set_fusion_strategy(self, strategy: FusionStrategy):
        """Change the fusion strategy."""
        self.fusion_strategy = strategy
        self.result_fusion.set_strategy(strategy)
        logger.info(f"Updated fusion strategy to: {strategy.value}")
    
    async def get_retrieval_stats(self) -> Dict[str, Any]:
        """Get comprehensive retrieval statistics."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Get stats from individual retrievers
            vector_stats_task = self.vector_retriever.get_retrieval_stats()
            keyword_stats_task = self.keyword_retriever.get_index_stats()
            
            vector_stats, keyword_stats = await asyncio.gather(
                vector_stats_task, keyword_stats_task
            )
            
            return {
                "vector_retrieval": vector_stats,
                "keyword_retrieval": keyword_stats,
                "configuration": {
                    "fusion_strategy": self.fusion_strategy.value,
                    "default_mode": self.default_mode.value,
                    "vector_weight": self.vector_weight,
                    "keyword_weight": self.keyword_weight,
                    "min_vector_score": self.min_vector_score,
                    "min_keyword_score": self.min_keyword_score
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting retrieval stats: {e}", exc_info=True)
            return {}
    
    async def health_check(self) -> Dict[str, bool]:
        """Check health of all retrieval components."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Check health of individual components
            vector_health_task = self.vector_retriever.health_check()
            keyword_health_task = self.keyword_retriever.health_check()
            
            vector_healthy, keyword_healthy = await asyncio.gather(
                vector_health_task, keyword_health_task
            )
            
            return {
                "vector_retriever": vector_healthy,
                "keyword_retriever": keyword_healthy,
                "overall": vector_healthy and keyword_healthy
            }
            
        except Exception as e:
            logger.error(f"Hybrid retriever health check failed: {e}")
            return {
                "vector_retriever": False,
                "keyword_retriever": False,
                "overall": False
            }
    
    async def close(self):
        """Close all retrieval components."""
        try:
            await asyncio.gather(
                self.vector_retriever.close(),
                self.keyword_retriever.close()
            )
            
            self._initialized = False
            logger.info("Hybrid retriever closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing hybrid retriever: {e}", exc_info=True)
