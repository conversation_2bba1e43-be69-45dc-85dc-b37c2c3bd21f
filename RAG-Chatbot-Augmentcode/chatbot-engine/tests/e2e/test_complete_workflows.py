"""End-to-end tests for complete system workflows."""

import pytest
import asyncio
import json
import time
from typing import Dict, List, Any
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
import httpx

from src.online_pipeline.api.main import create_app
from src.shared.models import (
    QueryRequest, DataSource, SourceType, ProcessingStatus,
    DocumentChunk, QueryResponse
)


@pytest.mark.e2e
class TestCompleteDataIngestionWorkflow:
    """Test complete data ingestion workflow from source to query."""
    
    @pytest.fixture
    def app(self):
        """Create FastAPI app for testing."""
        return create_app()
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def admin_headers(self):
        """Admin authentication headers."""
        return {"Authorization": "Bearer test-admin-token"}
    
    @pytest.fixture
    def sample_source_config(self):
        """Sample data source configuration."""
        return {
            "name": "Test Legal Website",
            "source_type": "website",
            "url": "https://example.com/legal-docs",
            "crawl_depth": 2,
            "enabled": True,
            "metadata": {
                "category": "legal",
                "language": "de",
                "priority": "high"
            }
        }
    
    @pytest.fixture
    def mock_crawled_content(self):
        """Mock crawled content for testing."""
        return [
            {
                "url": "https://example.com/legal-docs/contract-law",
                "title": "Vertragsrecht nach BGB",
                "content": """
                Ein Vertrag kommt durch zwei übereinstimmende Willenserklärungen zustande.
                Nach § 433 BGB verpflichtet sich der Verkäufer zur Übergabe der Sache.
                Die Willenserklärung muss bestimmt und bestimmbar sein.
                """,
                "metadata": {"section": "contract_law", "last_updated": "2024-01-15"}
            },
            {
                "url": "https://example.com/legal-docs/property-law",
                "title": "Sachenrecht Grundlagen",
                "content": """
                Das Eigentum ist das umfassendste Recht an einer Sache.
                Nach § 903 BGB kann der Eigentümer mit der Sache nach Belieben verfahren.
                Beschränkungen ergeben sich aus Gesetzen oder Rechten Dritter.
                """,
                "metadata": {"section": "property_law", "last_updated": "2024-01-20"}
            }
        ]
    
    @pytest.mark.asyncio
    async def test_complete_data_ingestion_workflow(
        self, 
        client, 
        admin_headers, 
        sample_source_config, 
        mock_crawled_content
    ):
        """Test complete workflow: Add source → Process → Query → Verify results."""
        
        # Step 1: Add new data source
        with patch('src.admin_interface.services.source_manager.AdminSourceManager.create_source') as mock_create:
            mock_source = DataSource(
                id="test-source-123",
                name=sample_source_config["name"],
                source_type=SourceType.WEBSITE,
                url=sample_source_config["url"],
                enabled=True,
                status=ProcessingStatus.PENDING,
                created_at=datetime.utcnow()
            )
            mock_create.return_value = mock_source
            
            response = client.post(
                "/admin/sources",
                json=sample_source_config,
                headers=admin_headers
            )
            
            assert response.status_code == 201
            source_data = response.json()
            assert source_data["name"] == sample_source_config["name"]
            assert source_data["status"] == "pending"
            source_id = source_data["id"]
        
        # Step 2: Start pipeline processing
        with patch('src.admin_interface.services.pipeline_manager.PipelineManager.start_pipeline') as mock_start:
            mock_task_id = "task-456"
            mock_start.return_value = {"task_id": mock_task_id, "status": "started"}
            
            response = client.post(
                "/admin/pipeline/start",
                json={
                    "mode": "incremental",
                    "source_ids": [source_id],
                    "force_reprocess": False
                },
                headers=admin_headers
            )
            
            assert response.status_code == 200
            task_data = response.json()
            assert task_data["task_id"] == mock_task_id
            task_id = task_data["task_id"]
        
        # Step 3: Monitor processing status
        with patch('src.admin_interface.services.pipeline_manager.PipelineManager.get_task_status') as mock_status:
            # Simulate processing progression
            mock_status.side_effect = [
                {"status": "STARTED", "progress": 25, "message": "Crawling website"},
                {"status": "STARTED", "progress": 50, "message": "Processing documents"},
                {"status": "STARTED", "progress": 75, "message": "Generating embeddings"},
                {"status": "SUCCESS", "progress": 100, "message": "Processing completed"}
            ]
            
            # Poll task status until completion
            for expected_progress in [25, 50, 75, 100]:
                response = client.get(
                    f"/admin/pipeline/tasks/{task_id}/status",
                    headers=admin_headers
                )
                
                assert response.status_code == 200
                status_data = response.json()
                assert status_data["progress"] == expected_progress
                
                if status_data["status"] == "SUCCESS":
                    break
                
                # Simulate polling delay
                await asyncio.sleep(0.1)
        
        # Step 4: Verify source status updated
        with patch('src.admin_interface.services.source_manager.AdminSourceManager.get_source') as mock_get:
            updated_source = DataSource(
                id=source_id,
                name=sample_source_config["name"],
                source_type=SourceType.WEBSITE,
                url=sample_source_config["url"],
                enabled=True,
                status=ProcessingStatus.COMPLETED,
                last_processed=datetime.utcnow(),
                document_count=2,
                chunk_count=6
            )
            mock_get.return_value = updated_source
            
            response = client.get(
                f"/admin/sources/{source_id}",
                headers=admin_headers
            )
            
            assert response.status_code == 200
            source_data = response.json()
            assert source_data["status"] == "completed"
            assert source_data["document_count"] == 2
            assert source_data["chunk_count"] == 6
        
        # Step 5: Test query against processed data
        with patch('src.online_pipeline.rag_pipeline.RAGPipeline.process_query') as mock_query:
            # Mock successful query response
            mock_response = QueryResponse(
                query="Was sind die Voraussetzungen für einen gültigen Vertrag?",
                response="""Nach deutschem Recht kommt ein Vertrag durch zwei übereinstimmende 
                Willenserklärungen zustande. Die wesentlichen Voraussetzungen sind:
                
                1. Angebot und Annahme
                2. Bestimmtheit der Willenserklärungen
                3. Rechtsbindungswille
                
                Nach § 433 BGB verpflichtet sich der Verkäufer zur Übergabe der Sache.""",
                sources=[
                    {
                        "url": "https://example.com/legal-docs/contract-law",
                        "title": "Vertragsrecht nach BGB",
                        "relevance_score": 0.92
                    }
                ],
                session_id="test-session-789",
                processing_time=0.45,
                retrieved_chunks=3,
                confidence_score=0.88
            )
            mock_query.return_value = mock_response
            
            query_request = {
                "query": "Was sind die Voraussetzungen für einen gültigen Vertrag?",
                "session_id": "test-session-789",
                "max_results": 5,
                "include_sources": True
            }
            
            response = client.post("/query", json=query_request)
            
            assert response.status_code == 200
            query_data = response.json()
            assert "Vertrag" in query_data["response"]
            assert "§ 433 BGB" in query_data["response"]
            assert len(query_data["sources"]) > 0
            assert query_data["confidence_score"] > 0.8
        
        # Step 6: Verify analytics data collection
        with patch('src.admin_interface.services.analytics_service.AnalyticsService.get_query_analytics') as mock_analytics:
            mock_analytics.return_value = {
                "total_queries": 1,
                "avg_response_time": 0.45,
                "query_types": {"legal_question": 1},
                "languages": {"de": 1},
                "success_rate": 1.0
            }
            
            response = client.get(
                "/admin/analytics/queries",
                params={"period": "1d"},
                headers=admin_headers
            )
            
            assert response.status_code == 200
            analytics_data = response.json()
            assert analytics_data["total_queries"] == 1
            assert analytics_data["success_rate"] == 1.0


@pytest.mark.e2e
class TestUserQueryWorkflows:
    """Test complete user query workflows and conversation handling."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)
    
    @pytest.mark.asyncio
    async def test_conversation_workflow(self, client):
        """Test multi-turn conversation with context handling."""
        session_id = "conversation-test-session"
        
        # Mock RAG pipeline for conversation
        with patch('src.online_pipeline.rag_pipeline.RAGPipeline.process_query') as mock_query:
            
            # First query: Initial legal question
            mock_query.return_value = QueryResponse(
                query="Was ist ein Kaufvertrag?",
                response="""Ein Kaufvertrag ist nach § 433 BGB ein Vertrag, durch den sich 
                der Verkäufer verpflichtet, dem Käufer eine Sache zu übergeben und das 
                Eigentum an der Sache zu verschaffen.""",
                sources=[{"url": "https://example.com/bgb", "title": "BGB § 433"}],
                session_id=session_id,
                processing_time=0.3,
                retrieved_chunks=2,
                confidence_score=0.95
            )
            
            response = client.post("/query", json={
                "query": "Was ist ein Kaufvertrag?",
                "session_id": session_id,
                "include_sources": True
            })
            
            assert response.status_code == 200
            first_response = response.json()
            assert "§ 433 BGB" in first_response["response"]
            assert first_response["confidence_score"] > 0.9
            
            # Second query: Follow-up question with context
            mock_query.return_value = QueryResponse(
                query="Welche Pflichten hat der Käufer?",
                response="""Der Käufer hat nach § 433 Abs. 2 BGB die Hauptpflicht, 
                den vereinbarten Kaufpreis zu zahlen und die gekaufte Sache abzunehmen. 
                Dies bezieht sich auf den zuvor erwähnten Kaufvertrag.""",
                sources=[{"url": "https://example.com/bgb", "title": "BGB § 433"}],
                session_id=session_id,
                processing_time=0.25,
                retrieved_chunks=2,
                confidence_score=0.92,
                context_used=True
            )
            
            response = client.post("/query", json={
                "query": "Welche Pflichten hat der Käufer?",
                "session_id": session_id,
                "include_sources": True
            })
            
            assert response.status_code == 200
            second_response = response.json()
            assert "Käufer" in second_response["response"]
            assert "Kaufpreis" in second_response["response"]
            assert second_response.get("context_used") is True
            
            # Third query: Complex follow-up
            mock_query.return_value = QueryResponse(
                query="Was passiert bei Nichterfüllung?",
                response="""Bei Nichterfüllung der Kaufvertragspflichten können verschiedene 
                Rechtsfolgen eintreten: Nacherfüllung (§ 439 BGB), Rücktritt (§ 323 BGB), 
                Schadensersatz (§ 280 BGB) oder Minderung (§ 441 BGB).""",
                sources=[
                    {"url": "https://example.com/bgb", "title": "BGB § 439"},
                    {"url": "https://example.com/bgb", "title": "BGB § 323"}
                ],
                session_id=session_id,
                processing_time=0.4,
                retrieved_chunks=4,
                confidence_score=0.89,
                context_used=True
            )
            
            response = client.post("/query", json={
                "query": "Was passiert bei Nichterfüllung?",
                "session_id": session_id,
                "include_sources": True
            })
            
            assert response.status_code == 200
            third_response = response.json()
            assert "Nichterfüllung" in third_response["response"]
            assert len(third_response["sources"]) >= 2
            assert third_response.get("context_used") is True
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(self, client):
        """Test error handling in query workflows."""
        
        # Test query with no results
        with patch('src.online_pipeline.rag_pipeline.RAGPipeline.process_query') as mock_query:
            mock_query.return_value = QueryResponse(
                query="Completely unrelated topic",
                response="Entschuldigung, ich konnte keine relevanten Informationen zu Ihrer Frage finden.",
                sources=[],
                session_id="error-test-session",
                processing_time=0.2,
                retrieved_chunks=0,
                confidence_score=0.1,
                error_type="no_relevant_results"
            )
            
            response = client.post("/query", json={
                "query": "Completely unrelated topic",
                "session_id": "error-test-session"
            })
            
            assert response.status_code == 200
            error_response = response.json()
            assert "keine relevanten Informationen" in error_response["response"]
            assert len(error_response["sources"]) == 0
            assert error_response["confidence_score"] < 0.5
        
        # Test service unavailable error
        with patch('src.online_pipeline.rag_pipeline.RAGPipeline.process_query') as mock_query:
            mock_query.side_effect = Exception("Service temporarily unavailable")
            
            response = client.post("/query", json={
                "query": "Test query",
                "session_id": "error-test-session"
            })
            
            assert response.status_code == 500
            error_data = response.json()
            assert "error" in error_data
    
    @pytest.mark.asyncio
    async def test_concurrent_user_sessions(self, client):
        """Test handling of concurrent user sessions."""
        
        with patch('src.online_pipeline.rag_pipeline.RAGPipeline.process_query') as mock_query:
            
            def mock_query_response(query_request):
                """Generate mock response based on session."""
                session_id = query_request.session_id
                return QueryResponse(
                    query=query_request.query,
                    response=f"Response for session {session_id}: Legal information about the query.",
                    sources=[{"url": "https://example.com/legal", "title": "Legal Source"}],
                    session_id=session_id,
                    processing_time=0.3,
                    retrieved_chunks=2,
                    confidence_score=0.85
                )
            
            mock_query.side_effect = lambda req: mock_query_response(req)
            
            # Simulate concurrent requests from different sessions
            sessions = ["session-1", "session-2", "session-3"]
            queries = [
                "Was ist ein Vertrag?",
                "Wie funktioniert Eigentum?",
                "Was sind Schuldverhältnisse?"
            ]
            
            # Send concurrent requests
            responses = []
            for i, (session, query) in enumerate(zip(sessions, queries)):
                response = client.post("/query", json={
                    "query": query,
                    "session_id": session,
                    "include_sources": True
                })
                responses.append((session, response))
            
            # Verify all responses are successful and session-specific
            for session, response in responses:
                assert response.status_code == 200
                response_data = response.json()
                assert session in response_data["response"]
                assert response_data["session_id"] == session
                assert response_data["confidence_score"] > 0.8


@pytest.mark.e2e
class TestAdminOperationsWorkflow:
    """Test complete admin operations workflows."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)
    
    @pytest.fixture
    def admin_headers(self):
        """Admin authentication headers."""
        return {"Authorization": "Bearer test-admin-token"}
    
    @pytest.mark.asyncio
    async def test_system_monitoring_workflow(self, client, admin_headers):
        """Test complete system monitoring workflow."""
        
        # Test system status check
        with patch('src.admin_interface.services.system_monitor.SystemMonitor.get_system_status') as mock_status:
            mock_status.return_value = {
                "overall_status": "healthy",
                "services": {
                    "api": {"status": "healthy", "response_time": 0.05},
                    "redis": {"status": "healthy", "response_time": 0.02},
                    "milvus": {"status": "healthy", "response_time": 0.08}
                },
                "system_metrics": {
                    "cpu_percent": 45.2,
                    "memory_percent": 62.1,
                    "disk_percent": 78.5
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
            response = client.get("/admin/system/status", headers=admin_headers)
            
            assert response.status_code == 200
            status_data = response.json()
            assert status_data["overall_status"] == "healthy"
            assert "services" in status_data
            assert "system_metrics" in status_data
        
        # Test metrics history
        with patch('src.admin_interface.services.system_monitor.SystemMonitor.get_metrics_history') as mock_history:
            mock_history.return_value = {
                "metrics": [
                    {
                        "timestamp": (datetime.utcnow() - timedelta(minutes=5)).isoformat(),
                        "cpu_percent": 42.1,
                        "memory_percent": 58.3,
                        "response_time": 0.12
                    },
                    {
                        "timestamp": datetime.utcnow().isoformat(),
                        "cpu_percent": 45.2,
                        "memory_percent": 62.1,
                        "response_time": 0.15
                    }
                ],
                "period": "1h",
                "interval": "5m"
            }
            
            response = client.get(
                "/admin/system/metrics/history",
                params={"period": "1h", "interval": "5m"},
                headers=admin_headers
            )
            
            assert response.status_code == 200
            history_data = response.json()
            assert len(history_data["metrics"]) == 2
            assert history_data["period"] == "1h"
    
    @pytest.mark.asyncio
    async def test_bulk_source_management_workflow(self, client, admin_headers):
        """Test bulk source management operations."""
        
        # Create multiple sources first
        source_ids = []
        with patch('src.admin_interface.services.source_manager.AdminSourceManager.create_source') as mock_create:
            for i in range(3):
                mock_source = DataSource(
                    id=f"bulk-source-{i}",
                    name=f"Bulk Test Source {i}",
                    source_type=SourceType.WEBSITE,
                    url=f"https://example.com/source-{i}",
                    enabled=True,
                    status=ProcessingStatus.COMPLETED
                )
                mock_create.return_value = mock_source
                
                response = client.post(
                    "/admin/sources",
                    json={
                        "name": f"Bulk Test Source {i}",
                        "source_type": "website",
                        "url": f"https://example.com/source-{i}",
                        "enabled": True
                    },
                    headers=admin_headers
                )
                
                assert response.status_code == 201
                source_ids.append(response.json()["id"])
        
        # Test bulk disable
        with patch('src.admin_interface.services.source_manager.AdminSourceManager.bulk_update_sources') as mock_bulk:
            mock_bulk.return_value = {
                "updated_count": 3,
                "failed_count": 0,
                "updated_sources": source_ids
            }
            
            response = client.post(
                "/admin/sources/bulk/disable",
                json={"source_ids": source_ids},
                headers=admin_headers
            )
            
            assert response.status_code == 200
            bulk_data = response.json()
            assert bulk_data["updated_count"] == 3
            assert bulk_data["failed_count"] == 0
        
        # Test bulk enable
        with patch('src.admin_interface.services.source_manager.AdminSourceManager.bulk_update_sources') as mock_bulk:
            mock_bulk.return_value = {
                "updated_count": 3,
                "failed_count": 0,
                "updated_sources": source_ids
            }
            
            response = client.post(
                "/admin/sources/bulk/enable",
                json={"source_ids": source_ids},
                headers=admin_headers
            )
            
            assert response.status_code == 200
            bulk_data = response.json()
            assert bulk_data["updated_count"] == 3
