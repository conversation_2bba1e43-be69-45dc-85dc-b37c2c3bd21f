"""Integration tests for the complete RAG pipeline."""

import pytest
import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from datetime import datetime

from src.online_pipeline.rag_pipeline import RAGPipeline
from src.shared.models import QueryRequest, QueryResponse, ProcessedQuery, DocumentChunk
from src.online_pipeline.retrieval.result_fusion import FusedResult


class TestRAGPipelineIntegration:
    """Integration test cases for RAGPipeline."""
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = MagicMock()
        settings.default_retrieval_top_k = 10
        settings.enable_session_management = True
        settings.enable_caching = True
        settings.google_api_key = "test-api-key"
        settings.redis_host = "localhost"
        settings.redis_port = 6379
        return settings
    
    @pytest.fixture
    def sample_document_chunk(self):
        """Create a sample document chunk."""
        return DocumentChunk(
            id="chunk-123",
            content="Ein Vertrag ist eine rechtliche Vereinbarung zwischen zwei oder mehr Parteien nach deutschem Recht.",
            source_id="source-456",
            source_url="https://example.com/bgb-kommentar",
            metadata={
                "title": "BGB Kommentar - Vertragsrecht",
                "section": "§ 433",
                "author": "Legal Expert"
            },
            created_at=datetime.utcnow()
        )
    
    @pytest.fixture
    def sample_fused_results(self, sample_document_chunk):
        """Create sample fused retrieval results."""
        fused_result = FusedResult(
            chunk=sample_document_chunk,
            final_score=0.85,
            rank=1
        )
        fused_result.add_component_result("vector", 0.90, 1, [])
        fused_result.add_component_result("keyword", 2.3, 1, ["vertrag", "rechtliche"])
        return [fused_result]
    
    @pytest.fixture
    def sample_query_response(self):
        """Create a sample query response."""
        return QueryResponse(
            answer="Ein Vertrag ist eine rechtliche Vereinbarung zwischen zwei oder mehr Parteien. Nach deutschem Recht ist ein Vertrag gemäß § 433 BGB ein Schuldverhältnis, bei dem sich eine Partei verpflichtet, eine Sache zu übergeben und das Eigentum daran zu verschaffen.",
            sources=[
                {
                    "title": "BGB Kommentar - Vertragsrecht",
                    "url": "https://example.com/bgb-kommentar",
                    "excerpt": "Ein Vertrag ist eine rechtliche Vereinbarung..."
                }
            ],
            session_id="test-session-123",
            processing_time=1.5,
            metadata={
                "model": "gemini-1.5-flash",
                "query_type": "definition_request",
                "citation_count": 1,
                "retrieval_methods": ["vector", "keyword"]
            }
        )
    
    @pytest.fixture
    async def rag_pipeline(self, mock_settings):
        """Create a RAGPipeline instance with mocked components."""
        with patch('src.online_pipeline.rag_pipeline.get_settings', return_value=mock_settings):
            pipeline = RAGPipeline()
            
            # Mock all components
            pipeline.query_processor = AsyncMock()
            pipeline.hybrid_retriever = AsyncMock()
            pipeline.response_generator = AsyncMock()
            pipeline.session_manager = AsyncMock()
            pipeline.cache_manager = AsyncMock()
            
            # Mock initialization
            pipeline.query_processor.process_query = AsyncMock()
            pipeline.hybrid_retriever.initialize = AsyncMock()
            pipeline.hybrid_retriever.retrieve = AsyncMock()
            pipeline.response_generator.initialize = AsyncMock()
            pipeline.response_generator.generate_response = AsyncMock()
            pipeline.session_manager.initialize = AsyncMock()
            pipeline.session_manager.get_context_for_query = AsyncMock(return_value=[])
            pipeline.session_manager.add_conversation_turn = AsyncMock(return_value=True)
            pipeline.cache_manager.initialize = AsyncMock()
            pipeline.cache_manager.get_cached_processed_query = AsyncMock(return_value=None)
            pipeline.cache_manager.cache_processed_query = AsyncMock(return_value=True)
            
            return pipeline
    
    @pytest.mark.asyncio
    async def test_pipeline_initialization(self, rag_pipeline):
        """Test that the pipeline initializes all components correctly."""
        await rag_pipeline.initialize()
        
        assert rag_pipeline._initialized
        rag_pipeline.hybrid_retriever.initialize.assert_called_once()
        rag_pipeline.response_generator.initialize.assert_called_once()
        rag_pipeline.session_manager.initialize.assert_called_once()
        rag_pipeline.cache_manager.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_complete_query_processing_flow(self, rag_pipeline, sample_fused_results, sample_query_response):
        """Test the complete query processing flow."""
        # Setup test data
        request = QueryRequest(
            query="Was ist ein Vertrag nach deutschem Recht?",
            session_id="test-session-123",
            max_results=5
        )
        
        processed_query = ProcessedQuery(
            original_query=request.query,
            sanitized_query=request.query,
            query_type="definition_request",
            complexity="simple",
            key_terms=["vertrag", "deutschem", "recht"],
            entities={"laws": [], "dates": [], "amounts": [], "organizations": []},
            query_variations=[request.query, "vertrag deutschem recht"],
            search_terms={
                "vector_search": request.query,
                "keyword_search": "vertrag deutschem recht",
                "hybrid_terms": ["vertrag", "deutschem", "recht"],
                "boost_terms": ["vertrag", "deutschem", "recht"]
            },
            session_id=request.session_id,
            processing_time=0.1,
            metadata={"language": "de", "word_count": 7}
        )
        
        # Setup mocks
        rag_pipeline.query_processor.process_query.return_value = processed_query
        rag_pipeline.hybrid_retriever.retrieve.return_value = sample_fused_results
        rag_pipeline.response_generator.generate_response.return_value = sample_query_response
        rag_pipeline._initialized = True
        
        # Execute pipeline
        response = await rag_pipeline.process_query(request)
        
        # Verify the flow
        rag_pipeline.query_processor.process_query.assert_called_once_with(request)
        rag_pipeline.session_manager.get_context_for_query.assert_called_once_with("test-session-123")
        rag_pipeline.hybrid_retriever.retrieve.assert_called_once()
        rag_pipeline.response_generator.generate_response.assert_called_once()
        rag_pipeline.session_manager.add_conversation_turn.assert_called_once()
        
        # Verify response
        assert isinstance(response, QueryResponse)
        assert response.session_id == "test-session-123"
        assert "pipeline_processing_time" in response.metadata
        assert "query_processing_time" in response.metadata
        assert "retrieval_count" in response.metadata
    
    @pytest.mark.asyncio
    async def test_query_processing_with_caching(self, rag_pipeline, sample_fused_results, sample_query_response):
        """Test query processing with caching enabled."""
        request = QueryRequest(query="Was ist ein Vertrag?")
        
        # Setup cached processed query
        cached_processed_query = ProcessedQuery(
            original_query=request.query,
            sanitized_query=request.query,
            query_type="definition_request",
            complexity="simple",
            key_terms=["vertrag"],
            entities={},
            query_variations=[],
            search_terms={},
            processing_time=0.05,
            metadata={"cached": True}
        )
        
        # Setup mocks
        rag_pipeline.cache_manager.get_cached_processed_query.return_value = cached_processed_query
        rag_pipeline.hybrid_retriever.retrieve.return_value = sample_fused_results
        rag_pipeline.response_generator.generate_response.return_value = sample_query_response
        rag_pipeline._initialized = True
        
        # Execute pipeline
        response = await rag_pipeline.process_query(request)
        
        # Verify caching behavior
        rag_pipeline.cache_manager.get_cached_processed_query.assert_called_once_with(request.query)
        rag_pipeline.query_processor.process_query.assert_not_called()  # Should use cached version
        
        # Verify response
        assert isinstance(response, QueryResponse)
    
    @pytest.mark.asyncio
    async def test_streaming_query_processing(self, rag_pipeline, sample_fused_results):
        """Test streaming query processing."""
        request = QueryRequest(query="Was ist ein Vertrag?")
        
        processed_query = ProcessedQuery(
            original_query=request.query,
            sanitized_query=request.query,
            query_type="definition_request",
            complexity="simple",
            key_terms=["vertrag"],
            entities={},
            query_variations=[],
            search_terms={},
            processing_time=0.1,
            metadata={}
        )
        
        # Mock streaming response
        async def mock_streaming_response(*args, **kwargs):
            chunks = ["Ein ", "Vertrag ", "ist ", "eine ", "rechtliche ", "Vereinbarung."]
            for chunk in chunks:
                yield chunk
        
        # Setup mocks
        rag_pipeline.query_processor.process_query.return_value = processed_query
        rag_pipeline.hybrid_retriever.retrieve.return_value = sample_fused_results
        rag_pipeline.response_generator.generate_streaming_response.return_value = mock_streaming_response()
        rag_pipeline._initialized = True
        
        # Execute streaming pipeline
        chunks = []
        async for chunk in rag_pipeline.process_streaming_query(request):
            chunks.append(chunk)
        
        # Verify streaming behavior
        rag_pipeline.query_processor.process_query.assert_called_once()
        rag_pipeline.hybrid_retriever.retrieve.assert_called_once()
        rag_pipeline.response_generator.generate_streaming_response.assert_called_once()
        
        # Verify chunks
        assert len(chunks) == 6
        assert "".join(chunks) == "Ein Vertrag ist eine rechtliche Vereinbarung."
    
    @pytest.mark.asyncio
    async def test_session_management_integration(self, rag_pipeline):
        """Test session management integration."""
        rag_pipeline._initialized = True
        
        # Test session creation
        session_id = await rag_pipeline.create_session({"user_id": "test-user"})
        rag_pipeline.session_manager.create_session.assert_called_once_with({"user_id": "test-user"})
        
        # Test conversation history retrieval
        await rag_pipeline.get_conversation_history("test-session", limit=10)
        rag_pipeline.session_manager.get_conversation_history.assert_called_once_with("test-session", 10)
    
    @pytest.mark.asyncio
    async def test_response_regeneration(self, rag_pipeline, sample_fused_results, sample_query_response):
        """Test response regeneration with feedback."""
        request = QueryRequest(query="Was ist ein Vertrag?")
        feedback = "Please provide more details about contract formation."
        
        processed_query = ProcessedQuery(
            original_query=request.query,
            sanitized_query=request.query,
            query_type="definition_request",
            complexity="simple",
            key_terms=["vertrag"],
            entities={},
            query_variations=[],
            search_terms={},
            processing_time=0.1,
            metadata={}
        )
        
        # Setup mocks
        rag_pipeline.query_processor.process_query.return_value = processed_query
        rag_pipeline.hybrid_retriever.retrieve.return_value = sample_fused_results
        rag_pipeline.response_generator.regenerate_response.return_value = sample_query_response
        rag_pipeline._initialized = True
        
        # Execute regeneration
        response = await rag_pipeline.regenerate_response(request, feedback)
        
        # Verify regeneration flow
        rag_pipeline.response_generator.regenerate_response.assert_called_once()
        
        # Verify response
        assert isinstance(response, QueryResponse)
    
    @pytest.mark.asyncio
    async def test_pipeline_health_check(self, rag_pipeline):
        """Test pipeline health check."""
        # Setup component health checks
        rag_pipeline.hybrid_retriever.health_check.return_value = {"overall": True, "vector_retriever": True, "keyword_retriever": True}
        rag_pipeline.response_generator.health_check.return_value = True
        rag_pipeline.session_manager.health_check.return_value = True
        rag_pipeline.cache_manager.health_check.return_value = True
        rag_pipeline._initialized = True
        
        # Execute health check
        health = await rag_pipeline.health_check()
        
        # Verify health check calls
        rag_pipeline.hybrid_retriever.health_check.assert_called_once()
        rag_pipeline.response_generator.health_check.assert_called_once()
        rag_pipeline.session_manager.health_check.assert_called_once()
        rag_pipeline.cache_manager.health_check.assert_called_once()
        
        # Verify health status
        assert health["overall"] is True
        assert "components" in health
        assert health["components"]["retrieval"]["overall"] is True
        assert health["components"]["generation"] is True
        assert health["components"]["session_management"] is True
        assert health["components"]["caching"] is True
    
    @pytest.mark.asyncio
    async def test_pipeline_stats(self, rag_pipeline):
        """Test pipeline statistics collection."""
        # Setup component stats
        rag_pipeline.hybrid_retriever.get_retrieval_stats.return_value = {"total_vectors": 1000}
        rag_pipeline.response_generator.get_generation_stats.return_value = {"model_name": "gemini-1.5-flash"}
        rag_pipeline.session_manager.get_session_stats.return_value = {"total_sessions": 50}
        rag_pipeline.cache_manager.get_cache_stats.return_value = {"cache_counts": {"queries": 100}}
        rag_pipeline._initialized = True
        
        # Execute stats collection
        stats = await rag_pipeline.get_pipeline_stats()
        
        # Verify stats calls
        rag_pipeline.hybrid_retriever.get_retrieval_stats.assert_called_once()
        rag_pipeline.response_generator.get_generation_stats.assert_called_once()
        rag_pipeline.session_manager.get_session_stats.assert_called_once()
        rag_pipeline.cache_manager.get_cache_stats.assert_called_once()
        
        # Verify stats structure
        assert "pipeline_config" in stats
        assert "retrieval_stats" in stats
        assert "generation_stats" in stats
        assert "session_stats" in stats
        assert "cache_stats" in stats
        
        assert stats["pipeline_config"]["caching_enabled"] is True
        assert stats["pipeline_config"]["session_management_enabled"] is True
    
    @pytest.mark.asyncio
    async def test_error_handling_in_pipeline(self, rag_pipeline):
        """Test error handling throughout the pipeline."""
        request = QueryRequest(query="Test query")
        
        # Setup query processor to raise an error
        rag_pipeline.query_processor.process_query.side_effect = Exception("Query processing error")
        rag_pipeline._initialized = True
        
        # Execute pipeline and expect error
        with pytest.raises(Exception) as exc_info:
            await rag_pipeline.process_query(request)
        
        assert "Query processing error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_pipeline_auto_initialization(self, rag_pipeline, sample_fused_results, sample_query_response):
        """Test that pipeline auto-initializes when not initialized."""
        request = QueryRequest(query="Test query")
        
        processed_query = ProcessedQuery(
            original_query=request.query,
            sanitized_query=request.query,
            query_type="general_legal",
            complexity="simple",
            key_terms=[],
            entities={},
            query_variations=[],
            search_terms={},
            processing_time=0.1,
            metadata={}
        )
        
        # Setup mocks
        rag_pipeline.query_processor.process_query.return_value = processed_query
        rag_pipeline.hybrid_retriever.retrieve.return_value = sample_fused_results
        rag_pipeline.response_generator.generate_response.return_value = sample_query_response
        rag_pipeline._initialized = False  # Not initialized
        
        # Execute pipeline
        response = await rag_pipeline.process_query(request)
        
        # Verify initialization was called
        rag_pipeline.hybrid_retriever.initialize.assert_called()
        rag_pipeline.response_generator.initialize.assert_called()
        rag_pipeline.session_manager.initialize.assert_called()
        rag_pipeline.cache_manager.initialize.assert_called()
        
        # Verify response
        assert isinstance(response, QueryResponse)
    
    @pytest.mark.asyncio
    async def test_pipeline_cleanup(self, rag_pipeline):
        """Test pipeline cleanup and resource management."""
        rag_pipeline._initialized = True
        
        # Execute cleanup
        await rag_pipeline.close()
        
        # Verify all components are closed
        rag_pipeline.hybrid_retriever.close.assert_called_once()
        rag_pipeline.response_generator.close.assert_called_once()
        rag_pipeline.session_manager.close.assert_called_once()
        rag_pipeline.cache_manager.close.assert_called_once()
        
        # Verify pipeline is marked as not initialized
        assert not rag_pipeline._initialized
