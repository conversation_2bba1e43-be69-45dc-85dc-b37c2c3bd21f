"""Pytest configuration and fixtures."""

import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Add the project root to handle relative imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from chatbot_engine.src.online_pipeline.api.main import create_app
    from chatbot_engine.src.shared.config import get_settings
except ImportError:
    # Fallback for direct imports
    try:
        from online_pipeline.api.main import create_app
        from shared.config import get_settings
    except ImportError:
        # Mock the imports for testing
        def create_app():
            from fastapi import FastAPI
            app = FastAPI()

            @app.get("/health")
            def health():
                return {"status": "healthy"}

            return app

        def get_settings():
            from unittest.mock import MagicMock
            settings = MagicMock()
            settings.google_api_key = "test-key"
            settings.redis_host = "localhost"
            settings.redis_port = 6379
            return settings


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def settings():
    """Get test settings."""
    return get_settings()


@pytest.fixture
def app():
    """Create FastAPI app for testing."""
    return create_app()


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
async def async_client(app):
    """Create async test client."""
    from httpx import AsyncClient
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sample_query_request():
    """Sample query request for testing."""
    return {
        "query": "What are the requirements for a valid contract under German law?",
        "session_id": "test_session_123",
        "max_results": 5,
        "include_sources": True
    }


@pytest.fixture
def sample_admin_source_request():
    """Sample admin source request for testing."""
    return {
        "name": "Test Legal Document",
        "source_type": "website",
        "url": "https://example.com/legal-doc",
        "crawl_depth": 2,
        "enabled": True,
        "metadata": {"category": "test"}
    }


@pytest.fixture
def mock_document_chunks():
    """Mock document chunks for testing."""
    from shared.models import DocumentChunk
    from datetime import datetime
    
    return [
        DocumentChunk(
            id="chunk_1",
            content="This is the first test chunk about German contract law.",
            source_id="test_source_1",
            source_url="https://example.com/doc1",
            metadata={"title": "Contract Law Basics", "section": "1"},
            created_at=datetime.utcnow()
        ),
        DocumentChunk(
            id="chunk_2",
            content="This is the second test chunk about legal requirements.",
            source_id="test_source_1",
            source_url="https://example.com/doc1",
            metadata={"title": "Legal Requirements", "section": "2"},
            created_at=datetime.utcnow()
        )
    ]


@pytest.fixture
def mock_data_sources():
    """Mock data sources for testing."""
    from shared.models import DataSource, SourceType, ProcessingStatus
    from datetime import datetime
    
    return [
        DataSource(
            id="source_1",
            name="BGB Test",
            source_type=SourceType.WEBSITE,
            url="https://example.com/bgb",
            enabled=True,
            status=ProcessingStatus.COMPLETED,
            last_processed=datetime.utcnow()
        ),
        DataSource(
            id="source_2",
            name="Test PDF",
            source_type=SourceType.PDF,
            path="/test/path/document.pdf",
            enabled=True,
            status=ProcessingStatus.PENDING
        )
    ]
