"""Performance and load testing for the RAG Legal Chatbot system."""

import random
import time
import json
from typing import List, Dict, Any
from locust import HttpUser, task, between, events
from locust.env import Environment
from locust.stats import stats_printer, stats_history
from locust.log import setup_logging
import gevent


class QueryUser(HttpUser):
    """Simulates a user making queries to the chatbot."""
    
    wait_time = between(1, 5)  # Wait 1-5 seconds between requests
    
    def on_start(self):
        """Initialize user session."""
        self.session_id = f"perf_test_session_{random.randint(1000, 9999)}"
        self.query_count = 0
        
        # Sample legal queries for testing
        self.legal_queries = [
            "Was sind die Voraussetzungen für einen gültigen Kaufvertrag?",
            "Wie funktioniert die Eigentumsübertragung bei Immobilien?",
            "Was ist der Unterschied zwischen Anfechtung und Widerruf?",
            "Welche Rechte hat ein Mieter bei Mietmängeln?",
            "Was sind die Voraussetzungen für eine Kündigung?",
            "Wie wird Schadensersatz nach BGB berechnet?",
            "Was ist eine Bürgschaft und welche Arten gibt es?",
            "Welche Formvorschriften gelten für Verträge?",
            "Was sind die Unterschiede zwischen Kauf und Miete?",
            "Wie funktioniert das Erbrecht in Deutschland?"
        ]
    
    @task(8)
    def query_chatbot(self):
        """Send a query to the chatbot (80% of requests)."""
        query = random.choice(self.legal_queries)
        
        payload = {
            "query": query,
            "session_id": self.session_id,
            "max_results": 5,
            "include_sources": True
        }
        
        with self.client.post(
            "/query",
            json=payload,
            headers={"Content-Type": "application/json"},
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response_data = response.json()
                
                # Validate response structure
                if "response" in response_data and "session_id" in response_data:
                    response.success()
                    self.query_count += 1
                    
                    # Track response time
                    response_time = response.elapsed.total_seconds() * 1000
                    if response_time > 1000:  # Log slow responses
                        print(f"Slow response: {response_time}ms for query: {query[:50]}...")
                else:
                    response.failure("Invalid response structure")
            else:
                response.failure(f"HTTP {response.status_code}")
    
    @task(1)
    def health_check(self):
        """Check system health (10% of requests)."""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: HTTP {response.status_code}")
    
    @task(1)
    def get_session_history(self):
        """Get session history (10% of requests)."""
        with self.client.get(
            f"/sessions/{self.session_id}/history",
            catch_response=True
        ) as response:
            if response.status_code in [200, 404]:  # 404 is acceptable for new sessions
                response.success()
            else:
                response.failure(f"Session history failed: HTTP {response.status_code}")


class AdminUser(HttpUser):
    """Simulates admin user operations."""
    
    wait_time = between(5, 15)  # Admin operations are less frequent
    
    def on_start(self):
        """Initialize admin session."""
        self.admin_token = "test-admin-token"
        self.headers = {
            "Authorization": f"Bearer {self.admin_token}",
            "Content-Type": "application/json"
        }
    
    @task(3)
    def check_system_status(self):
        """Check system status."""
        with self.client.get(
            "/admin/system/status",
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"System status failed: HTTP {response.status_code}")
    
    @task(2)
    def get_analytics(self):
        """Get system analytics."""
        endpoints = [
            "/admin/analytics/queries",
            "/admin/analytics/sessions",
            "/admin/analytics/performance"
        ]
        
        endpoint = random.choice(endpoints)
        with self.client.get(
            endpoint,
            headers=self.headers,
            params={"period": "1h"},
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Analytics failed: HTTP {response.status_code}")
    
    @task(1)
    def list_sources(self):
        """List data sources."""
        with self.client.get(
            "/admin/sources",
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"List sources failed: HTTP {response.status_code}")
    
    @task(1)
    def get_pipeline_status(self):
        """Get pipeline status."""
        with self.client.get(
            "/admin/pipeline/statistics",
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Pipeline status failed: HTTP {response.status_code}")


class ConversationUser(HttpUser):
    """Simulates users having multi-turn conversations."""
    
    wait_time = between(2, 8)
    
    def on_start(self):
        """Initialize conversation session."""
        self.session_id = f"conv_test_session_{random.randint(1000, 9999)}"
        self.conversation_step = 0
        
        # Conversation flows for testing
        self.conversation_flows = [
            [
                "Was ist ein Kaufvertrag?",
                "Welche Pflichten hat der Verkäufer?",
                "Was passiert bei Nichterfüllung?",
                "Kann der Käufer vom Vertrag zurücktreten?"
            ],
            [
                "Was ist Eigentum?",
                "Wie wird Eigentum übertragen?",
                "Was sind dingliche Rechte?",
                "Welche Beschränkungen gibt es?"
            ],
            [
                "Was ist eine Bürgschaft?",
                "Welche Arten von Bürgschaften gibt es?",
                "Wann haftet der Bürge?",
                "Wie kann sich der Bürge befreien?"
            ]
        ]
        
        self.current_flow = random.choice(self.conversation_flows)
    
    @task
    def continue_conversation(self):
        """Continue the conversation flow."""
        if self.conversation_step < len(self.current_flow):
            query = self.current_flow[self.conversation_step]
            
            payload = {
                "query": query,
                "session_id": self.session_id,
                "max_results": 3,
                "include_sources": True
            }
            
            with self.client.post(
                "/query",
                json=payload,
                headers={"Content-Type": "application/json"},
                catch_response=True
            ) as response:
                if response.status_code == 200:
                    response_data = response.json()
                    if "response" in response_data:
                        response.success()
                        self.conversation_step += 1
                        
                        # If conversation is complete, start a new one
                        if self.conversation_step >= len(self.current_flow):
                            self.conversation_step = 0
                            self.current_flow = random.choice(self.conversation_flows)
                            self.session_id = f"conv_test_session_{random.randint(1000, 9999)}"
                    else:
                        response.failure("Invalid response structure")
                else:
                    response.failure(f"HTTP {response.status_code}")


# Performance test scenarios
class LightLoadTest(HttpUser):
    """Light load test scenario (10-20 users)."""
    tasks = [QueryUser]
    weight = 1


class MediumLoadTest(HttpUser):
    """Medium load test scenario (50-100 users)."""
    tasks = [QueryUser, AdminUser]
    weight = 1


class HeavyLoadTest(HttpUser):
    """Heavy load test scenario (100+ users)."""
    tasks = [QueryUser, AdminUser, ConversationUser]
    weight = 1


# Custom event handlers for performance monitoring
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """Log performance metrics for analysis."""
    if exception:
        print(f"Request failed: {request_type} {name} - {exception}")
    elif response_time > 1000:  # Log requests taking more than 1 second
        print(f"Slow request: {request_type} {name} - {response_time}ms")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Initialize performance test."""
    print("Starting performance test...")
    print(f"Target host: {environment.host}")
    print(f"User classes: {[cls.__name__ for cls in environment.user_classes]}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Finalize performance test and generate report."""
    print("Performance test completed!")
    
    # Print summary statistics
    stats = environment.stats
    print(f"\nPerformance Test Summary:")
    print(f"Total requests: {stats.total.num_requests}")
    print(f"Failed requests: {stats.total.num_failures}")
    print(f"Average response time: {stats.total.avg_response_time:.2f}ms")
    print(f"95th percentile: {stats.total.get_response_time_percentile(0.95):.2f}ms")
    print(f"99th percentile: {stats.total.get_response_time_percentile(0.99):.2f}ms")
    print(f"Requests per second: {stats.total.current_rps:.2f}")
    
    # Check performance thresholds
    avg_response_time = stats.total.avg_response_time
    p95_response_time = stats.total.get_response_time_percentile(0.95)
    failure_rate = stats.total.num_failures / max(stats.total.num_requests, 1)
    
    print(f"\nPerformance Thresholds:")
    print(f"Average response time: {avg_response_time:.2f}ms (target: <500ms)")
    print(f"95th percentile: {p95_response_time:.2f}ms (target: <1000ms)")
    print(f"Failure rate: {failure_rate:.2%} (target: <1%)")
    
    # Determine if test passed
    test_passed = (
        avg_response_time < 500 and
        p95_response_time < 1000 and
        failure_rate < 0.01
    )
    
    print(f"\nTest Result: {'PASSED' if test_passed else 'FAILED'}")


# Utility functions for running specific test scenarios
def run_load_test(host: str, users: int, spawn_rate: int, duration: int):
    """Run a load test with specified parameters."""
    setup_logging("INFO", None)
    
    # Setup the Environment with desired settings
    env = Environment(user_classes=[QueryUser, AdminUser], host=host)
    
    # Start the test
    env.create_local_runner()
    
    # Start a greenlet that periodically outputs the current stats
    gevent.spawn(stats_printer(env.stats))
    
    # Start a greenlet that save current stats to history
    gevent.spawn(stats_history, env.runner)
    
    # Start the test
    env.runner.start(users, spawn_rate=spawn_rate)
    
    # Run for specified duration
    gevent.spawn_later(duration, lambda: env.runner.quit())
    
    # Wait for the greenlets
    env.runner.greenlet.join()
    
    return env.stats


def run_spike_test(host: str, base_users: int, spike_users: int, spike_duration: int):
    """Run a spike test to simulate sudden traffic increases."""
    setup_logging("INFO", None)
    
    env = Environment(user_classes=[QueryUser], host=host)
    env.create_local_runner()
    
    # Start with base load
    print(f"Starting with {base_users} users...")
    env.runner.start(base_users, spawn_rate=10)
    
    # Wait for stabilization
    gevent.sleep(30)
    
    # Spike to higher load
    print(f"Spiking to {spike_users} users...")
    env.runner.start(spike_users, spawn_rate=50)
    
    # Maintain spike
    gevent.sleep(spike_duration)
    
    # Return to base load
    print(f"Returning to {base_users} users...")
    env.runner.start(base_users, spawn_rate=10)
    
    # Wait for stabilization
    gevent.sleep(30)
    
    env.runner.quit()
    env.runner.greenlet.join()
    
    return env.stats


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python test_load_testing.py <test_type> [host]")
        print("Test types: load, spike, conversation")
        sys.exit(1)
    
    test_type = sys.argv[1]
    host = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8000"
    
    if test_type == "load":
        print("Running load test...")
        stats = run_load_test(host, users=50, spawn_rate=5, duration=300)
    elif test_type == "spike":
        print("Running spike test...")
        stats = run_spike_test(host, base_users=20, spike_users=100, spike_duration=60)
    elif test_type == "conversation":
        print("Running conversation test...")
        env = Environment(user_classes=[ConversationUser], host=host)
        env.create_local_runner()
        env.runner.start(30, spawn_rate=3)
        gevent.sleep(300)  # Run for 5 minutes
        env.runner.quit()
        env.runner.greenlet.join()
    else:
        print(f"Unknown test type: {test_type}")
        sys.exit(1)
