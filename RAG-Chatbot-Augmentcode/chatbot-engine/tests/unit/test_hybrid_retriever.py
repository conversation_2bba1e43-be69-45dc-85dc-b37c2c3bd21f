"""Unit tests for the hybrid retriever."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from src.online_pipeline.retrieval.hybrid_retriever import HybridRetriever, RetrievalMode
from src.online_pipeline.retrieval.result_fusion import FusionStrategy, FusedResult
from src.online_pipeline.retrieval.vector_retriever import Retri<PERSON><PERSON><PERSON>ult
from src.online_pipeline.retrieval.keyword_retriever import KeywordR<PERSON>rievalResult
from src.shared.models import ProcessedQuery, DocumentChunk


class TestHybridRetriever:
    """Test cases for HybridRetriever."""
    
    @pytest.fixture
    def mock_vector_retriever(self):
        """Create a mock vector retriever."""
        mock = AsyncMock()
        mock.initialize = AsyncMock()
        mock.retrieve = AsyncMock()
        mock.health_check = AsyncMock(return_value=True)
        mock.close = AsyncMock()
        return mock
    
    @pytest.fixture
    def mock_keyword_retriever(self):
        """Create a mock keyword retriever."""
        mock = AsyncMock()
        mock.initialize = AsyncMock()
        mock.retrieve = AsyncMock()
        mock.health_check = AsyncMock(return_value=True)
        mock.close = AsyncMock()
        return mock
    
    @pytest.fixture
    def mock_result_fusion(self):
        """Create a mock result fusion."""
        mock = MagicMock()
        mock.fuse_results = MagicMock()
        return mock
    
    @pytest.fixture
    def hybrid_retriever(self, mock_vector_retriever, mock_keyword_retriever, mock_result_fusion):
        """Create a HybridRetriever instance with mocked components."""
        retriever = HybridRetriever()
        retriever.vector_retriever = mock_vector_retriever
        retriever.keyword_retriever = mock_keyword_retriever
        retriever.result_fusion = mock_result_fusion
        return retriever
    
    @pytest.fixture
    def sample_processed_query(self):
        """Create a sample processed query."""
        return ProcessedQuery(
            original_query="Was ist ein Vertrag?",
            sanitized_query="Was ist ein Vertrag?",
            query_type="definition_request",
            complexity="simple",
            key_terms=["vertrag"],
            entities={"laws": [], "dates": [], "amounts": [], "organizations": []},
            query_variations=["Was ist ein Vertrag?", "vertrag"],
            search_terms={
                "vector_search": "Was ist ein Vertrag?",
                "keyword_search": "vertrag",
                "hybrid_terms": ["vertrag"],
                "boost_terms": ["vertrag"]
            },
            processing_time=0.1,
            metadata={"language": "de"}
        )
    
    @pytest.fixture
    def sample_document_chunk(self):
        """Create a sample document chunk."""
        return DocumentChunk(
            id="chunk-123",
            content="Ein Vertrag ist eine rechtliche Vereinbarung zwischen zwei oder mehr Parteien.",
            source_id="source-456",
            source_url="https://example.com/legal-doc",
            metadata={"title": "BGB Kommentar", "section": "§ 433"},
            created_at=datetime.utcnow()
        )
    
    @pytest.fixture
    def sample_vector_results(self, sample_document_chunk):
        """Create sample vector retrieval results."""
        return [
            RetrievalResult(
                chunk=sample_document_chunk,
                score=0.95,
                rank=1
            )
        ]
    
    @pytest.fixture
    def sample_keyword_results(self, sample_document_chunk):
        """Create sample keyword retrieval results."""
        return [
            KeywordRetrievalResult(
                chunk=sample_document_chunk,
                score=2.5,
                rank=1,
                matched_terms=["vertrag"]
            )
        ]
    
    @pytest.fixture
    def sample_fused_results(self, sample_document_chunk):
        """Create sample fused results."""
        fused_result = FusedResult(
            chunk=sample_document_chunk,
            final_score=0.85,
            rank=1
        )
        fused_result.add_component_result("vector", 0.95, 1, [])
        fused_result.add_component_result("keyword", 2.5, 1, ["vertrag"])
        return [fused_result]
    
    @pytest.mark.asyncio
    async def test_initialize(self, hybrid_retriever):
        """Test hybrid retriever initialization."""
        await hybrid_retriever.initialize()
        
        assert hybrid_retriever._initialized
        hybrid_retriever.vector_retriever.initialize.assert_called_once()
        hybrid_retriever.keyword_retriever.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_hybrid_retrieval(self, hybrid_retriever, sample_processed_query, 
                                   sample_vector_results, sample_keyword_results, 
                                   sample_fused_results):
        """Test hybrid retrieval mode."""
        # Setup mocks
        hybrid_retriever.vector_retriever.retrieve.return_value = sample_vector_results
        hybrid_retriever.keyword_retriever.retrieve.return_value = sample_keyword_results
        hybrid_retriever.result_fusion.fuse_results.return_value = sample_fused_results
        hybrid_retriever._initialized = True
        
        # Execute retrieval
        results = await hybrid_retriever.retrieve(
            processed_query=sample_processed_query,
            top_k=5,
            mode=RetrievalMode.HYBRID
        )
        
        # Verify calls
        hybrid_retriever.vector_retriever.retrieve.assert_called_once()
        hybrid_retriever.keyword_retriever.retrieve.assert_called_once()
        hybrid_retriever.result_fusion.fuse_results.assert_called_once()
        
        # Verify results
        assert len(results) == 1
        assert results[0].final_score == 0.85
        assert "vector" in results[0].retrieval_methods
        assert "keyword" in results[0].retrieval_methods
    
    @pytest.mark.asyncio
    async def test_vector_only_retrieval(self, hybrid_retriever, sample_processed_query, 
                                        sample_vector_results):
        """Test vector-only retrieval mode."""
        hybrid_retriever.vector_retriever.retrieve.return_value = sample_vector_results
        hybrid_retriever._initialized = True
        
        results = await hybrid_retriever.retrieve(
            processed_query=sample_processed_query,
            top_k=5,
            mode=RetrievalMode.VECTOR_ONLY
        )
        
        # Should only call vector retriever
        hybrid_retriever.vector_retriever.retrieve.assert_called_once()
        hybrid_retriever.keyword_retriever.retrieve.assert_not_called()
        
        # Verify results
        assert len(results) == 1
        assert results[0].final_score == 0.95  # Original vector score
        assert results[0].retrieval_methods == ["vector"]
    
    @pytest.mark.asyncio
    async def test_keyword_only_retrieval(self, hybrid_retriever, sample_processed_query, 
                                         sample_keyword_results):
        """Test keyword-only retrieval mode."""
        hybrid_retriever.keyword_retriever.retrieve.return_value = sample_keyword_results
        hybrid_retriever._initialized = True
        
        results = await hybrid_retriever.retrieve(
            processed_query=sample_processed_query,
            top_k=5,
            mode=RetrievalMode.KEYWORD_ONLY
        )
        
        # Should only call keyword retriever
        hybrid_retriever.keyword_retriever.retrieve.assert_called_once()
        hybrid_retriever.vector_retriever.retrieve.assert_not_called()
        
        # Verify results
        assert len(results) == 1
        assert results[0].final_score == 2.5  # Original keyword score
        assert results[0].retrieval_methods == ["keyword"]
    
    def test_determine_optimal_mode_definition_request(self, hybrid_retriever):
        """Test optimal mode determination for definition requests."""
        query = ProcessedQuery(
            original_query="Was ist ein Vertrag?",
            sanitized_query="Was ist ein Vertrag?",
            query_type="definition_request",
            complexity="simple",
            key_terms=["vertrag"],
            entities={"laws": ["BGB"]},
            query_variations=[],
            search_terms={},
            processing_time=0.1,
            metadata={}
        )
        
        # Set to adaptive mode
        hybrid_retriever.default_mode = RetrievalMode.ADAPTIVE
        
        mode = hybrid_retriever._determine_optimal_mode(query)
        assert mode == RetrievalMode.KEYWORD_ONLY
    
    def test_determine_optimal_mode_complex_legal(self, hybrid_retriever):
        """Test optimal mode determination for complex legal questions."""
        query = ProcessedQuery(
            original_query="Complex legal question",
            sanitized_query="Complex legal question",
            query_type="legal_question",
            complexity="complex",
            key_terms=["legal", "question"],
            entities={},
            query_variations=[],
            search_terms={},
            processing_time=0.1,
            metadata={}
        )
        
        hybrid_retriever.default_mode = RetrievalMode.ADAPTIVE
        
        mode = hybrid_retriever._determine_optimal_mode(query)
        assert mode == RetrievalMode.VECTOR_ONLY
    
    def test_determine_optimal_mode_many_terms(self, hybrid_retriever):
        """Test optimal mode determination for queries with many key terms."""
        query = ProcessedQuery(
            original_query="Query with many terms",
            sanitized_query="Query with many terms",
            query_type="general_legal",
            complexity="moderate",
            key_terms=["term1", "term2", "term3", "term4", "term5", "term6"],
            entities={},
            query_variations=[],
            search_terms={},
            processing_time=0.1,
            metadata={}
        )
        
        hybrid_retriever.default_mode = RetrievalMode.ADAPTIVE
        
        mode = hybrid_retriever._determine_optimal_mode(query)
        assert mode == RetrievalMode.HYBRID
    
    @pytest.mark.asyncio
    async def test_search_similar_to_chunk(self, hybrid_retriever, sample_vector_results):
        """Test finding similar chunks."""
        hybrid_retriever.vector_retriever.retrieve_similar_chunks.return_value = sample_vector_results
        hybrid_retriever._initialized = True
        
        results = await hybrid_retriever.search_similar_to_chunk("chunk-123", top_k=5)
        
        hybrid_retriever.vector_retriever.retrieve_similar_chunks.assert_called_once_with("chunk-123", 5)
        
        assert len(results) == 1
        assert results[0].retrieval_methods == ["vector_similarity"]
    
    def test_configure_weights(self, hybrid_retriever):
        """Test weight configuration."""
        hybrid_retriever.configure_weights(0.7, 0.3)
        
        assert hybrid_retriever.vector_weight == 0.7
        assert hybrid_retriever.keyword_weight == 0.3
    
    def test_configure_weights_normalization(self, hybrid_retriever):
        """Test weight normalization."""
        hybrid_retriever.configure_weights(3.0, 1.0)
        
        assert hybrid_retriever.vector_weight == 0.75
        assert hybrid_retriever.keyword_weight == 0.25
    
    def test_set_fusion_strategy(self, hybrid_retriever):
        """Test fusion strategy setting."""
        hybrid_retriever.set_fusion_strategy(FusionStrategy.WEIGHTED_SUM)
        
        assert hybrid_retriever.fusion_strategy == FusionStrategy.WEIGHTED_SUM
        hybrid_retriever.result_fusion.set_strategy.assert_called_once_with(FusionStrategy.WEIGHTED_SUM)
    
    @pytest.mark.asyncio
    async def test_refresh_indexes(self, hybrid_retriever):
        """Test index refresh."""
        hybrid_retriever._initialized = True
        
        await hybrid_retriever.refresh_indexes()
        
        hybrid_retriever.keyword_retriever.refresh_index.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_retrieval_stats(self, hybrid_retriever):
        """Test retrieval statistics."""
        hybrid_retriever.vector_retriever.get_retrieval_stats.return_value = {"vector_stats": "data"}
        hybrid_retriever.keyword_retriever.get_index_stats.return_value = {"keyword_stats": "data"}
        hybrid_retriever._initialized = True
        
        stats = await hybrid_retriever.get_retrieval_stats()
        
        assert "vector_retrieval" in stats
        assert "keyword_retrieval" in stats
        assert "configuration" in stats
        
        config = stats["configuration"]
        assert "fusion_strategy" in config
        assert "default_mode" in config
        assert "vector_weight" in config
        assert "keyword_weight" in config
    
    @pytest.mark.asyncio
    async def test_health_check_healthy(self, hybrid_retriever):
        """Test health check when all components are healthy."""
        hybrid_retriever._initialized = True
        
        health = await hybrid_retriever.health_check()
        
        assert health["overall"] is True
        assert health["vector_retriever"] is True
        assert health["keyword_retriever"] is True
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy(self, hybrid_retriever):
        """Test health check when components are unhealthy."""
        hybrid_retriever.vector_retriever.health_check.return_value = False
        hybrid_retriever._initialized = True
        
        health = await hybrid_retriever.health_check()
        
        assert health["overall"] is False
        assert health["vector_retriever"] is False
    
    @pytest.mark.asyncio
    async def test_close(self, hybrid_retriever):
        """Test closing the hybrid retriever."""
        hybrid_retriever._initialized = True
        
        await hybrid_retriever.close()
        
        hybrid_retriever.vector_retriever.close.assert_called_once()
        hybrid_retriever.keyword_retriever.close.assert_called_once()
        assert not hybrid_retriever._initialized
    
    @pytest.mark.asyncio
    async def test_retrieve_auto_initialize(self, hybrid_retriever, sample_processed_query):
        """Test that retrieve auto-initializes if not initialized."""
        hybrid_retriever._initialized = False
        hybrid_retriever.vector_retriever.retrieve.return_value = []
        hybrid_retriever.keyword_retriever.retrieve.return_value = []
        hybrid_retriever.result_fusion.fuse_results.return_value = []
        
        await hybrid_retriever.retrieve(sample_processed_query)
        
        # Should have called initialize
        hybrid_retriever.vector_retriever.initialize.assert_called_once()
        hybrid_retriever.keyword_retriever.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_retrieve_error_handling(self, hybrid_retriever, sample_processed_query):
        """Test error handling in retrieve method."""
        hybrid_retriever._initialized = True
        hybrid_retriever.vector_retriever.retrieve.side_effect = Exception("Vector error")
        
        with pytest.raises(Exception):
            await hybrid_retriever.retrieve(sample_processed_query)
