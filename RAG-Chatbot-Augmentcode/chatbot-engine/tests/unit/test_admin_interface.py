"""
Unit tests for admin interface services.

This module contains comprehensive unit tests for all admin interface
components including system monitoring, source management, pipeline
management, analytics, and configuration management.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from admin_interface.services.system_monitor import SystemMonitor, ServiceHealth, SystemMetrics
from admin_interface.services.source_manager import AdminSourceManager
from admin_interface.services.pipeline_manager import PipelineManager, PipelineMode
from admin_interface.services.analytics_service import AnalyticsService
from admin_interface.services.config_manager import ConfigManager


class TestSystemMonitor:
    """Test cases for SystemMonitor service."""
    
    @pytest.fixture
    async def system_monitor(self):
        """Create a SystemMonitor instance for testing."""
        monitor = SystemMonitor()
        # Mock the initialization to avoid actual connections
        monitor._redis_client = Mock()
        monitor._milvus_connected = True
        monitor._initialized = True
        return monitor
    
    @pytest.mark.asyncio
    async def test_check_redis_health(self, system_monitor):
        """Test Redis health check."""
        # Mock Redis ping
        system_monitor._redis_client.ping.return_value = True
        system_monitor._redis_client.info.return_value = {
            "connected_clients": 5,
            "used_memory_human": "10M",
            "uptime_in_seconds": 3600
        }
        
        health = await system_monitor._check_redis_health(0)
        
        assert health.name == "redis"
        assert health.status == "healthy"
        assert health.details["connected_clients"] == 5
    
    @pytest.mark.asyncio
    async def test_check_api_health(self, system_monitor):
        """Test API health check."""
        health = await system_monitor._check_api_health(0)
        
        assert health.name == "api"
        assert health.status == "healthy"
        assert health.response_time_ms is not None
    
    @pytest.mark.asyncio
    async def test_get_system_metrics(self, system_monitor):
        """Test system metrics collection."""
        with patch('psutil.cpu_percent', return_value=25.5), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk, \
             patch('psutil.net_connections', return_value=[1, 2, 3]):
            
            mock_memory.return_value.percent = 60.0
            mock_disk.return_value.percent = 45.0
            
            metrics = await system_monitor.get_system_metrics()
            
            assert metrics.cpu_usage_percent == 25.5
            assert metrics.memory_usage_percent == 60.0
            assert metrics.disk_usage_percent == 45.0
            assert metrics.active_connections == 3
    
    @pytest.mark.asyncio
    async def test_get_system_status(self, system_monitor):
        """Test comprehensive system status."""
        with patch.object(system_monitor, 'check_service_health') as mock_health, \
             patch.object(system_monitor, 'get_system_metrics') as mock_metrics:
            
            # Mock service health checks
            mock_health.side_effect = [
                ServiceHealth("redis", "healthy"),
                ServiceHealth("milvus", "healthy"),
                ServiceHealth("celery", "healthy"),
                ServiceHealth("api", "healthy")
            ]
            
            # Mock metrics
            mock_metrics.return_value = SystemMetrics(
                timestamp=datetime.now(),
                cpu_usage_percent=25.0,
                memory_usage_percent=60.0,
                disk_usage_percent=45.0,
                active_connections=3,
                total_requests=100,
                avg_response_time_ms=250.0,
                error_rate_percent=1.0
            )
            
            status = await system_monitor.get_system_status()
            
            assert status.overall_health == "healthy"
            assert len(status.services) == 4
            assert status.metrics.cpu_usage_percent == 25.0


class TestAdminSourceManager:
    """Test cases for AdminSourceManager service."""
    
    @pytest.fixture
    async def source_manager(self):
        """Create an AdminSourceManager instance for testing."""
        manager = AdminSourceManager()
        # Mock the source manager
        manager.source_manager = AsyncMock()
        manager._initialized = True
        return manager
    
    @pytest.mark.asyncio
    async def test_list_sources(self, source_manager):
        """Test listing data sources."""
        # Mock source data
        mock_sources = [
            Mock(id="1", name="Test Source 1", enabled=True),
            Mock(id="2", name="Test Source 2", enabled=False)
        ]
        source_manager.source_manager.get_all_sources.return_value = mock_sources
        
        sources = await source_manager.list_sources()
        
        assert len(sources) == 2
        assert sources[0].name == "Test Source 1"
        source_manager.source_manager.get_all_sources.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_source(self, source_manager):
        """Test creating a new data source."""
        source_data = {
            "name": "New Source",
            "source_type": "website",
            "url": "https://example.com",
            "enabled": True
        }
        
        mock_created_source = Mock(id="new_id", name="New Source")
        source_manager.source_manager.add_source.return_value = mock_created_source
        
        result = await source_manager.create_source(source_data)
        
        assert result.name == "New Source"
        source_manager.source_manager.add_source.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_validate_source_data(self, source_manager):
        """Test source data validation."""
        # Test missing required fields
        with pytest.raises(ValueError, match="Missing required field"):
            await source_manager._validate_source_data({})
        
        # Test invalid source type
        with pytest.raises(ValueError, match="Invalid source type"):
            await source_manager._validate_source_data({
                "name": "Test",
                "source_type": "invalid"
            })
        
        # Test valid data
        valid_data = {
            "name": "Test Source",
            "source_type": "website",
            "url": "https://example.com"
        }
        # Should not raise an exception
        await source_manager._validate_source_data(valid_data)


class TestPipelineManager:
    """Test cases for PipelineManager service."""
    
    @pytest.fixture
    async def pipeline_manager(self):
        """Create a PipelineManager instance for testing."""
        manager = PipelineManager()
        # Mock the pipeline
        manager.pipeline = AsyncMock()
        manager._initialized = True
        return manager
    
    @pytest.mark.asyncio
    async def test_start_pipeline(self, pipeline_manager):
        """Test starting a pipeline."""
        with patch('admin_interface.services.pipeline_manager.process_source_task') as mock_task:
            mock_result = Mock()
            mock_result.id = "task_123"
            mock_task.delay.return_value = mock_result
            
            result = await pipeline_manager.start_pipeline(
                mode=PipelineMode.FULL,
                source_ids=["source1", "source2"]
            )
            
            assert result["task_id"] == "task_123"
            assert result["mode"] == "full"
            assert result["source_ids"] == ["source1", "source2"]
    
    @pytest.mark.asyncio
    async def test_get_pipeline_statistics(self, pipeline_manager):
        """Test getting pipeline statistics."""
        # Add some mock task history
        pipeline_manager._task_history = [
            {"status": "SUCCESS", "started_at": datetime.now()},
            {"status": "FAILURE", "started_at": datetime.now()},
            {"status": "SUCCESS", "started_at": datetime.now()}
        ]
        pipeline_manager._active_tasks = {
            "task1": {"status": "PENDING"}
        }
        
        stats = await pipeline_manager.get_pipeline_statistics()
        
        assert stats["total_tasks"] == 4
        assert stats["active_tasks"] == 1
        assert stats["completed_tasks"] == 3
        assert stats["success_rate_percent"] == 50.0


class TestAnalyticsService:
    """Test cases for AnalyticsService service."""
    
    @pytest.fixture
    async def analytics_service(self):
        """Create an AnalyticsService instance for testing."""
        service = AnalyticsService()
        # Mock Redis client
        service._redis_client = AsyncMock()
        service._initialized = True
        return service
    
    @pytest.mark.asyncio
    async def test_record_query(self, analytics_service):
        """Test recording query analytics."""
        query_data = {
            "query": "Test query",
            "query_type": "definition",
            "language": "en",
            "response_time_ms": 250
        }
        
        await analytics_service.record_query(query_data)
        
        # Verify Redis operations were called
        assert analytics_service._redis_client.hincrby.call_count > 0
        assert analytics_service._redis_client.lpush.call_count > 0
    
    @pytest.mark.asyncio
    async def test_get_usage_summary(self, analytics_service):
        """Test getting usage summary."""
        with patch.object(analytics_service, 'get_query_analytics') as mock_query, \
             patch.object(analytics_service, 'get_session_analytics') as mock_session, \
             patch.object(analytics_service, 'get_performance_analytics') as mock_perf:
            
            mock_query.return_value = {"total_queries": 100}
            mock_session.return_value = {"current_sessions": {"total_active": 5}}
            mock_perf.return_value = {"response_times": {"avg_response_time_ms": 250}}
            
            summary = await analytics_service.get_usage_summary()
            
            assert "queries" in summary
            assert "sessions" in summary
            assert "performance" in summary


class TestConfigManager:
    """Test cases for ConfigManager service."""
    
    @pytest.fixture
    async def config_manager(self):
        """Create a ConfigManager instance for testing."""
        manager = ConfigManager()
        # Mock config cache
        manager._config_cache = {
            "app_config": {"app": {"name": "test"}},
            "sources": {"sources": []}
        }
        return manager
    
    @pytest.mark.asyncio
    async def test_get_config(self, config_manager):
        """Test getting configuration."""
        config = await config_manager.get_config("app_config")
        
        assert config["app"]["name"] == "test"
    
    @pytest.mark.asyncio
    async def test_validate_app_config(self, config_manager):
        """Test application configuration validation."""
        # Test valid config
        valid_config = {
            "app": {"name": "test"},
            "api": {"port": 8000},
            "logging": {"level": "INFO"},
            "milvus": {"host": "localhost", "port": 19530, "collection_name": "test"},
            "redis": {"host": "localhost", "port": 6379, "db": 0}
        }
        
        result = await config_manager._validate_app_config(valid_config)
        assert result["valid"] is True
        assert len(result["errors"]) == 0
        
        # Test invalid config
        invalid_config = {
            "app": {"name": "test"}
            # Missing required sections
        }
        
        result = await config_manager._validate_app_config(invalid_config)
        assert result["valid"] is False
        assert len(result["errors"]) > 0
    
    @pytest.mark.asyncio
    async def test_backup_and_restore_config(self, config_manager):
        """Test configuration backup and restore."""
        # Test backup
        backup_result = await config_manager.backup_config("app_config")
        assert backup_result["success"] is True
        assert len(manager._config_history) > 0
        
        # Test restore
        backup_timestamp = backup_result["backup_timestamp"]
        restore_result = await config_manager.restore_config("app_config", backup_timestamp)
        assert restore_result["success"] is True


# Integration test for admin routes
class TestAdminRoutes:
    """Integration tests for admin API routes."""
    
    @pytest.mark.asyncio
    async def test_admin_health_endpoint(self):
        """Test admin health check endpoint."""
        # This would require setting up FastAPI test client
        # For now, just test that the endpoint function works
        from online_pipeline.api.routes.admin import admin_health_check
        
        result = await admin_health_check()
        assert result["status"] == "healthy"
        assert "admin_interface" in result


if __name__ == "__main__":
    pytest.main([__file__])
