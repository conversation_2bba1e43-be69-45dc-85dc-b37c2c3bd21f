apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rag-legal-chatbot
      component: redis
  template:
    metadata:
      labels:
        app: rag-legal-chatbot
        component: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --maxmemory
        - "1gb"
        - --maxmemory-policy
        - "allkeys-lru"
        - --save
        - "900 1"
        - --save
        - "300 10"
        - --save
        - "60 10000"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /usr/local/etc/redis/redis.conf
          subPath: redis.conf
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 999
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-data-pvc
      - name: redis-config
        configMap:
          name: redis-config
      restartPolicy: Always
      securityContext:
        fsGroup: 999
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: redis
spec:
  selector:
    app: rag-legal-chatbot
    component: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: redis
data:
  redis.conf: |
    # Redis production configuration
    
    # Network
    bind 0.0.0.0
    port 6379
    tcp-backlog 511
    timeout 0
    tcp-keepalive 300
    
    # General
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    databases 16
    
    # Snapshotting
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir /data
    
    # Replication
    replica-serve-stale-data yes
    replica-read-only yes
    repl-diskless-sync no
    repl-diskless-sync-delay 5
    repl-disable-tcp-nodelay no
    replica-priority 100
    
    # Security
    # requirepass your_password_here
    
    # Memory management
    maxmemory 1gb
    maxmemory-policy allkeys-lru
    maxmemory-samples 5
    
    # Lazy freeing
    lazyfree-lazy-eviction no
    lazyfree-lazy-expire no
    lazyfree-lazy-server-del no
    replica-lazy-flush no
    
    # Append only file
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    aof-load-truncated yes
    aof-use-rdb-preamble yes
    
    # Lua scripting
    lua-time-limit 5000
    
    # Slow log
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    
    # Latency monitor
    latency-monitor-threshold 0
    
    # Event notification
    notify-keyspace-events ""
    
    # Advanced config
    hash-max-ziplist-entries 512
    hash-max-ziplist-value 64
    list-max-ziplist-size -2
    list-compress-depth 0
    set-max-intset-entries 512
    zset-max-ziplist-entries 128
    zset-max-ziplist-value 64
    hll-sparse-max-bytes 3000
    stream-node-max-bytes 4096
    stream-node-max-entries 100
    activerehashing yes
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
    hz 10
    dynamic-hz yes
    aof-rewrite-incremental-fsync yes
    rdb-save-incremental-fsync yes
