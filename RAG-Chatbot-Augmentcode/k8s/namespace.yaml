apiVersion: v1
kind: Namespace
metadata:
  name: rag-chatbot
  labels:
    name: rag-chatbot
    environment: production
    app: rag-legal-chatbot
  annotations:
    description: "RAG Legal Chatbot production namespace"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: rag-chatbot-quota
  namespace: rag-chatbot
spec:
  hard:
    requests.cpu: "8"
    requests.memory: 16Gi
    limits.cpu: "16"
    limits.memory: 32Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: rag-chatbot-limits
  namespace: rag-chatbot
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
