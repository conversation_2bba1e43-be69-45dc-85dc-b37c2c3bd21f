# Production requirements - optimized for performance and security
# Core LangChain Ecosystem
langchain==0.1.20
langchain-community==0.0.38
langchain-core==0.1.52
langchain-google-genai==1.0.6
langchain-cohere==0.1.5

# Web Framework - Production versions
fastapi==0.111.0
uvicorn[standard]==0.30.1
pydantic==2.7.4
pydantic-settings==2.3.4

# Vector Database
pymilvus==2.4.4

# Document Processing - Production stable versions
pypdf==4.2.0
beautifulsoup4==4.12.3
lxml==5.2.2

# Search and Ranking
rank-bm25==0.2.2
cohere==5.5.8

# Background Processing
celery==5.4.0
redis==5.0.7
kombu==5.3.7

# HTTP Client
httpx==0.27.0
aiohttp==3.9.5

# Utilities
pyyaml==6.0.1
python-dotenv==1.0.1
requests==2.32.3
numpy==1.26.4

# File handling
aiofiles==23.2.1
python-multipart==0.0.9

# Template engine
jinja2==3.1.4

# Security
cryptography==42.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Monitoring and metrics
prometheus-client==0.20.0
psutil==5.9.8

# Logging
structlog==24.2.0
python-json-logger==2.0.7

# Performance
orjson==3.10.5
ujson==5.10.0

# Production WSGI server
gunicorn==22.0.0

# Health checks
healthcheck==1.3.3

# Environment and configuration
environs==11.0.0

# Date/time handling
python-dateutil==2.9.0.post0

# Async utilities
asyncio-mqtt==0.16.2
aioredis==2.0.1

# Data validation
marshmallow==3.21.3

# Rate limiting
slowapi==0.1.9

# CORS handling
fastapi-cors==0.0.6

# Request ID tracking
asgi-correlation-id==4.3.1

# Timezone handling
pytz==2024.1

# Memory optimization
pympler==0.9

# Process management
supervisor==4.2.5
